<template>
  <div class="app-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>应用管理</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area mb-4">
        <el-form :model="searchForm" inline>
          <el-form-item label="应用名称">
            <el-input
              v-model="searchForm.appName"
              placeholder="请输入应用名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="应用编码">
            <el-input
              v-model="searchForm.appCode"
              placeholder="请输入应用编码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="应用类型">
            <el-input
              v-model="searchForm.appType"
              placeholder="请输入应用类型"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleSearch"
              :icon="useRenderIcon('EP-Search')"
            >
              搜索
            </el-button>
            <el-button @click="handleReset" :icon="useRenderIcon('EP-Refresh')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-area mb-4">
        <el-button
          type="primary"
          @click="handleAdd"
          :icon="useRenderIcon('EP-Plus')"
        >
          新增应用
        </el-button>
        <el-button
          type="success"
          @click="batchAddApps"
          :icon="useRenderIcon('EP-Upload')"
          :loading="submitLoading"
        >
          批量添加应用
        </el-button>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        height="666"
      >
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column prop="appName" label="应用名称" min-width="120" />
        <el-table-column prop="appCode" label="应用编码" min-width="120" />
        <el-table-column
          prop="appUrl"
          label="应用地址"
          min-width="200"
          show-overflow-tooltip
        />
        <!-- <el-table-column prop="appIcon" label="应用图标" min-width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.appIcon"
              :src="row.appIcon"
              style="width: 40px; height: 40px"
              fit="cover"
              :preview-src-list="[row.appIcon]"
            />
            <span v-else>-</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="appType" label="应用类型" width="100">
          <template #default="{ row }">
            {{ row.appType || "-" }}
          </template>
        </el-table-column>
        <el-table-column prop="appGroup" label="应用分组" width="100">
          <template #default="{ row }">
            {{ row.appGroup || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-area mt-4">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
      :close-on-click-modal="false"
    >
      <AppForm ref="appFormRef" v-model="formData" :key="formKey" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import AppForm from "./components/AppForm.vue";
import {
  queryAppInfos,
  saveAppInfo,
  deleteAppInfo,
  type AppInfo,
  type AppManageQueryCondition
} from "./api/AppManage";

// 响应式数据
const loading = ref(false);
const submitLoading = ref(false);
const tableData = ref<AppInfo[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const formKey = ref(Date.now());

// 搜索表单
const searchForm = reactive<AppManageQueryCondition>({
  appName: "",
  appCode: "",
  appType: "",
  pageNun: 1,
  pageSize: 10
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表单数据
const formData = reactive<AppInfo>({
  id: "",
  appName: "",
  appCode: "",
  appUrl: "",
  appIcon: "",
  appType: null,
  appGroup: null
});

// 表单引用
const appFormRef = ref();

// 监听formData变化
watch(
  formData,
  newValue => {
    console.log("主页面 formData 变化:", JSON.stringify(newValue, null, 2));
  },
  { deep: true }
);

// 获取应用列表
const getAppList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      pageNun: pagination.current,
      pageSize: pagination.size
    };
    const response = await queryAppInfos(params);
    if (response.status === "0") {
      tableData.value = response.data.records;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取应用列表失败");
    }
  } catch (error) {
    ElMessage.error("获取应用列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  getAppList();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    appName: "",
    appCode: "",
    appType: "",
    pageNun: 1,
    pageSize: 10
  });
  pagination.current = 1;
  getAppList();
};

// 新增
const handleAdd = () => {
  dialogTitle.value = "新增应用";
  isEdit.value = false;

  // 先清空表单数据
  resetFormData();

  // 更新key值，强制重新渲染AppForm组件
  formKey.value = Date.now();
  dialogVisible.value = true;
};

// 编辑
const handleEdit = (row: AppInfo) => {
  console.log("编辑行数据:", JSON.stringify(row, null, 2));

  dialogTitle.value = "编辑应用";
  isEdit.value = true;

  // 深拷贝数据进行回填
  const editData = {
    id: row.id || "",
    appName: row.appName || "",
    appCode: row.appCode || "",
    appUrl: row.appUrl || "",
    appIcon: row.appIcon || "",
    appType: row.appType || null,
    appGroup: row.appGroup || null
  };

  console.log("回填的数据:", JSON.stringify(editData, null, 2));
  Object.assign(formData, editData);
  console.log("formData更新后:", JSON.stringify(formData, null, 2));

  // 更新key值，强制重新渲染AppForm组件
  formKey.value = Date.now();
  dialogVisible.value = true;
};

// 删除
const handleDelete = async (row: AppInfo) => {
  try {
    await ElMessageBox.confirm(`确定要删除应用"${row.appName}"吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const response = await deleteAppInfo(row.id);
    if (response.status === "0") {
      ElMessage.success("删除成功");
      getAppList();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    // 用户取消删除
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!appFormRef.value) return;

  submitLoading.value = true;
  try {
    const isValid = await appFormRef.value.validate();
    if (!isValid) return;

    // 直接从AppForm组件获取最新的表单数据
    const submitData = appFormRef.value.getCurrentData();

    // 调试日志：查看提交的数据
    console.log("从AppForm获取的数据:", JSON.stringify(submitData, null, 2));
    console.log("主页面formData:", JSON.stringify(formData, null, 2));
    console.log("是否为编辑模式:", isEdit.value);

    const response = await saveAppInfo(submitData);
    if (response.status === "0") {
      ElMessage.success(isEdit.value ? "更新成功" : "新增成功");
      dialogVisible.value = false;
      getAppList();
    } else {
      ElMessage.error(response.msg || "操作失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    submitLoading.value = false;
  }
};

// 取消按钮处理
const handleCancel = () => {
  dialogVisible.value = false;
};

// 关闭对话框
const handleDialogClose = () => {
  // 关闭对话框时，只重置表单的验证状态，不重置数据
  nextTick(() => {
    appFormRef.value?.resetFields();
  });
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: "",
    appName: "",
    appCode: "",
    appUrl: "",
    appIcon: "",
    appType: null,
    appGroup: null
  });
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  getAppList();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  getAppList();
};

// 批量添加应用数据
const batchAddApps = async () => {
  // 告警管理应用数据
  const alarmManagementApps = [
    {
      id: "",
      name: 'alarm_query_asset',
      appName: '实时告警',
      appCode: 'alarm_query_asset',
      appUrl: '/pages/asset/alarm_query_asset',
      appIcon: "/static/asset/view_asset.png",
      appType: '内部应用',
      appGroup: '告警管理'
    },
    {
      id: "",
      name: 'alarm_statistics_asset',
      appName: '告警统计',
      appCode: 'alarm_statistics_asset',
      appUrl: '/pages/asset/alarm_statistics_asset',
      appIcon: "/static/asset/register_asset.png",
      appType: '内部应用',
      appGroup: '告警管理'
    },
    {
      id: "",
      name: 'alarm_query_asset_search',
      appName: '告警查询',
      appCode: 'alarm_query_asset_search',
      appUrl: '/pages/asset/alarm_query_asset?type=告警查询',
      appIcon: "/static/asset/view_asset.png",
      appType: '内部应用',
      appGroup: '告警管理'
    }
  ];

  // 工单管理应用数据
  const workOrderManagementApps = [
    {
      id: "",
      name: 'my_todo_list',
      appName: '我的待办',
      appCode: 'my_todo_list',
      appUrl: '/pages/list/my_list?index=0',
      appIcon: "/static/list/my_todo_list.png",
      appType: '内部应用',
      appGroup: '工单管理'
    },
    {
      id: "",
      name: 'my_tracked_list',
      appName: '我的已办',
      appCode: 'my_tracked_list',
      appUrl: '/pages/list/my_list?index=1',
      appIcon: "/static/list/my_tracked_list.png",
      appType: '内部应用',
      appGroup: '工单管理'
    },
    {
      id: "",
      name: 'my_create_list',
      appName: '创建工单',
      appCode: 'my_create_list',
      appUrl: '/pages/list/draft_list?procKey=templatea&procName=新工单',
      appIcon: "/static/list/all_list.png",
      appType: '内部应用',
      appGroup: '工单管理'
    },
    {
      id: "",
      name: 'work_order_statistics',
      appName: '工单统计',
      appCode: 'work_order_statistics',
      appUrl: '/pages/list/workOrderStatistics',
      appIcon: "/static/list/all_list.png",
      appType: '内部应用',
      appGroup: '工单管理'
    },
    {
      id: "",
      name: 'my_notice_list',
      appName: '查看通知',
      appCode: 'my_notice_list',
      appUrl: '/pages/list/notice?procKey=2',
      appIcon: "/static/APP-new/view_notification_icon.svg",
      appType: '内部应用',
      appGroup: '工单管理'
    },
    {
      id: "",
      name: 'my_created_notice_list',
      appName: '创建通知',
      appCode: 'my_created_notice_list',
      appUrl: '/pages/list/noticeInfoCreate?type=创建通知',
      appIcon: "/static/APP-new/create_notification_icon.svg",
      appType: '内部应用',
      appGroup: '工单管理'
    }
  ];

  // 态势呈现应用数据
  const situationPresentationApps = [
    {
      id: "",
      name: 'entirety',
      appName: '整体态势',
      appCode: 'entirety',
      appUrl: '/pages/situationPresentation/entirety',
      appIcon: "/static/APP-new/overall_situation_icon.svg",
      appType: '内部应用',
      appGroup: '态势呈现'
    },
    {
      id: "",
      name: 'source',
      appName: '资源态势',
      appCode: 'source',
      appUrl: '/pages/situationPresentation/source',
      appIcon: "/static/APP-new/resource_situation_icon.svg",
      appType: '内部应用',
      appGroup: '态势呈现'
    },
    {
      id: "",
      name: 'running',
      appName: '运行态势',
      appCode: 'running',
      appUrl: '/pages/situationPresentation/running',
      appIcon: "/static/APP-new/operation_situation_icon.svg",
      appType: '内部应用',
      appGroup: '态势呈现'
    },
    {
      id: "",
      name: 'user',
      appName: '用户使用态势',
      appCode: 'user',
      appUrl: '/pages/situationPresentation/user',
      appIcon: "/static/APP-new/user_usage_situation_icon.svg",
      appType: '内部应用',
      appGroup: '态势呈现'
    },
    {
      id: "",
      name: 'officialDocument',
      appName: '公文应用态势',
      appCode: 'officialDocument',
      appUrl: '/pages/situationPresentation/officialDocument',
      appIcon: "/static/APP-new/document_application_situation_icon.svg",
      appType: '内部应用',
      appGroup: '态势呈现'
    }
  ];

  // 合并所有应用数据
  const allApps = [
    ...alarmManagementApps,
    ...workOrderManagementApps,
    ...situationPresentationApps
  ];

  let successCount = 0;
  let failCount = 0;
  const failedApps = [];

  ElMessage.info(`开始批量添加 ${allApps.length} 个应用...`);

  // 逐个添加应用
  for (const app of allApps) {
    try {
      const response = await saveAppInfo(app);
      if (response.status === "0") {
        successCount++;
        console.log(`成功添加应用: ${app.appName}`);
      } else {
        failCount++;
        failedApps.push({ app: app.appName, error: response.msg });
        console.error(`添加应用失败: ${app.appName}, 错误: ${response.msg}`);
      }
    } catch (error) {
      failCount++;
      failedApps.push({ app: app.appName, error: error.message || '未知错误' });
      console.error(`添加应用异常: ${app.appName}`, error);
    }

    // 添加小延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // 显示批量添加结果
  if (failCount === 0) {
    ElMessage.success(`批量添加完成！成功添加 ${successCount} 个应用`);
  } else {
    ElMessage.warning(`批量添加完成！成功: ${successCount} 个，失败: ${failCount} 个`);
    console.log('失败的应用详情:', failedApps);
  }

  // 刷新应用列表
  getAppList();
};

// 组件挂载时获取数据
onMounted(() => {
  getAppList();
});
</script>

<style scoped lang="scss">
@import "./styles/index.scss";
</style>
