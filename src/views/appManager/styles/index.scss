.app-manager {
  // padding: 20px;
  // background-color: #f5f7fa;
  // min-height: calc(100vh - 84px);

  .box-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }

  .search-area {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;

    .el-form-item {
      margin-bottom: 0;

      .el-form-item__label {
        color: #495057;
        font-weight: 500;
      }
    }
  }

  .action-area {
    margin-bottom: 20px;
  }

  .el-table {
    border-radius: 8px;
    overflow: hidden;
    height: 500px;

    .el-table__header {
      th {
        background-color: #fafafa !important;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__body {
      tr:hover {
        background-color: #f5f7fa;
      }

      td {
        padding: 12px 0;
      }
    }
  }

  .pagination-area {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .el-dialog {
    border-radius: 8px;

    .el-dialog__body {
      padding: 20px;
    }
  }

  .el-image {
    border-radius: 4px;
    overflow: hidden;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;

    .search-area {
      .el-form {
        .el-form-item {
          display: block;
          margin-bottom: 15px;

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }
    }

    .el-table {
      font-size: 12px;

      .el-table__body {
        td {
          padding: 12px 8px;
        }
      }
    }

    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }
  }
}

// 暗色主题适配
.dark {
  .app-manager {
    background-color: #1a1a1a;

    .box-card {
      background-color: #2d2d2d;
      border: 1px solid #404040;

      .card-header {
        color: #e5eaf3;
      }
    }

    .search-area {
      background-color: #2d2d2d;
      border-color: #404040;

      .el-form-item__label {
        color: #e5eaf3;
      }
    }

    .el-table {
      background-color: #2d2d2d;

      .el-table__header th {
        background-color: #404040 !important;
        color: #e5eaf3;
        border-bottom-color: #4a5568;
      }

      .el-table__body {
        tr {
          background-color: #2d2d2d;

          &:hover {
            background-color: #404040;
          }
        }

        td {
          border-bottom-color: #404040;
          color: #e5eaf3;
        }
      }
    }
  }
}
