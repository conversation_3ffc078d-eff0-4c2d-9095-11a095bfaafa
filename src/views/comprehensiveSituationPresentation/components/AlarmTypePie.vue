<template>
  <div class="alarm-type-pie-container">
    <div class="hot-apps-title">
      <div class="hot-apps-icon">
        <div></div>
        <div style="margin-left: 3px"></div>
      </div>
      <span class="hot-apps-text">告警类别-TOP5</span>
    </div>
    <!-- 图表容器始终存在，避免DOM元素的创建和销毁 -->
    <div ref="chartRef" class="alarm-type-pie"></div>
    <!-- 无数据时的占位层，覆盖在图表上方 -->
    <div v-if="!props.data || props.data.length === 0" class="no-data-overlay">
      <div class="no-data-text">暂无数据</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "AlarmTypePie"
});
</script>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { PieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import alarmTypeTitleImg from "../assets/CPU利用率趋势图-标题@3x(5).png";

// 注册必要的组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);

// 定义图表数据类型
interface AlarmTypeItem {
  name: string;
  value: number;
  color: string;
}

const props = defineProps<{
  data: AlarmTypeItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) {
    console.warn("AlarmTypePie: chartRef.value 不存在，无法初始化图表");
    return;
  }

  // 如果图表已存在，先销毁
  if (chart) {
    console.log("AlarmTypePie: 销毁现有图表实例");
    chart.dispose();
    chart = null;
  }

  console.log("AlarmTypePie: 创建新的图表实例");
  chart = echarts.init(chartRef.value);

  // 如果没有数据，只初始化空图表
  if (!props.data || props.data.length === 0) {
    console.log("AlarmTypePie: 初始化空图表");
    return;
  }

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e4e7ed",
      textStyle: {
        color: "#333333"
      }
    },
    legend: {
      orient: "vertical",
      right: "0%",
      top: "middle",
      itemWidth: 10,
      itemHeight: 10,
      icon: "circle",
      itemGap: 16,
      textStyle: {
        color: "#2c3e50",
        fontSize: 14
      }
    },
    series: [
      {
        name: "告警类型",
        type: "pie",
        radius: ["6%", "70%"],
        center: ["30%", "50%"],
        startAngle: 200,
        avoidLabelOverlap: false,
        roseType: "radius",
        itemStyle: {
          borderRadius: 0,
          borderColor: "#ffffff",
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  };

  chart.setOption(option);
  console.log("AlarmTypePie: 图表初始化完成，数据项数量:", props.data.length);
};

// 更新图表
const updateChart = () => {
  if (!chart) {
    console.warn("AlarmTypePie: 图表实例不存在，无法更新");
    return;
  }

  if (!props.data || props.data.length === 0) {
    console.log("AlarmTypePie: 数据为空，清空图表");
    chart.clear();
    return;
  }

  try {
    // 设置完整的图表配置，确保图表能正确渲染
    const option = {
      backgroundColor: "transparent",
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)",
        backgroundColor: "rgba(255, 255, 255, 0.95)",
        borderColor: "#e4e7ed",
        textStyle: {
          color: "#333333"
        }
      },
      legend: {
        orient: "vertical",
        right: "0%",
        top: "middle",
        itemWidth: 10,
        itemHeight: 10,
        icon: "circle",
        itemGap: 16,
        textStyle: {
          color: "#2c3e50",
          fontSize: 14
        }
      },
      series: [
        {
          name: "告警类型",
          type: "pie",
          radius: ["30%", "70%"],
          center: ["30%", "50%"],
          startAngle: 200,
          avoidLabelOverlap: false,
          roseType: "radius",
          itemStyle: {
            borderRadius: 0,
            borderColor: "#ffffff",
            borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)"
            }
          },
          labelLine: {
            show: false
          },
          data: props.data.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }
      ]
    };

    chart.setOption(option, true); // 第二个参数为true，不合并配置
    console.log("AlarmTypePie: 图表更新完成，数据项数量:", props.data.length);
  } catch (error) {
    console.error("AlarmTypePie: 更新图表时出错:", error);
    // 如果更新失败，尝试重新初始化
    initChart();
  }
};

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  chart && chart.resize();
};

// 监听数据变化，更新图表
watch(
  () => props.data,
  (newData, oldData) => {
    console.log("AlarmTypePie: 数据变化", {
      newData: newData?.length || 0,
      oldData: oldData?.length || 0,
      hasChart: !!chart
    });

    // 如果图表实例不存在，先初始化
    if (!chart) {
      console.log("AlarmTypePie: 图表实例不存在，重新初始化");
      initChart();
      return;
    }

    // 如果有数据，更新图表
    if (newData && newData.length > 0) {
      console.log("AlarmTypePie: 更新图表数据");
      updateChart();
    } else {
      console.log("AlarmTypePie: 数据为空，清空图表内容");
      // 数据为空时清空图表内容，但保持实例
      chart.clear();
    }
  },
  { deep: true }
);

// 组件挂载时初始化图表
onMounted(() => {
  console.log("AlarmTypePie: 组件挂载，开始初始化");
  // 确保DOM渲染完成后再初始化图表
  setTimeout(() => {
    initChart();
    window.addEventListener("resize", handleResize);
  }, 0);
});

// 组件卸载时清理图表实例
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.alarm-type-pie-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  .hot-apps-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .hot-apps-icon {
      font-size: 16px;
      margin-right: 6px;
      display: flex;
      div {
        height: 13px;
        width: 3px;
        background: #3296fb;
        border-radius: 3px;
        transform: rotate(16deg);
      }
    }

    .hot-apps-text {
      font-size: 14px;
      padding-top: 2px;
      font-weight: 600;
      color: #000000db;
    }
  }
  .alarm-title {
    margin-bottom: 5px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .alarm-type-pie {
    width: 100%;
    flex: 1;
    min-height: 160px;
    margin-top: 0px;
  }

  .no-data-overlay {
    position: absolute;
    top: 40px; /* 标题高度 */
    left: 8px;
    right: 8px;
    bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;

    .no-data-text {
      color: #999999;
      font-size: 14px;
      text-align: center;
    }
  }

  :deep(.el-tooltip__trigger) {
    display: flex;
    align-items: center;
  }
}
</style>
