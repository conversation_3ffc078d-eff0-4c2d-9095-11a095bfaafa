<template>
  <div class="system-topology-container">
    <div class="hot-apps-title">
      <div class="hot-apps-icon">
        <div></div>
        <div style="margin-left: 3px"></div>
      </div>
      <span class="hot-apps-text">系统架构</span>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">加载中...</div>
    </div>

    <!-- 错误状态或暂无数据状态 -->
    <div v-else-if="error || !hasData" class="no-data-placeholder">
      <div class="no-data-icon">
        <svg viewBox="0 0 24 24" width="48" height="48" fill="#d9d9d9">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
      <div class="no-data-text">{{ error || '暂无数据' }}</div>
      <div class="no-data-desc">当前机房或时间范围内没有相关数据</div>
    </div>

    <!-- 正常数据显示 -->
    <div v-else class="topology-content">
      <!-- 应用服务层 -->
      <div class="topology-layer">
        <div class="layer-label">应用服务层</div>
        <div class="layer-content">
          <div
            v-if="processedData.applicationLayer.length > 0"
            v-for="(app, index) in processedData.applicationLayer"
            :key="`app-${index}`"
            class="topology-node"

            @mouseenter="showTooltip($event, app)"
            @mouseleave="hideTooltip"
          >
            <img
              :src="getIconPath('应用服务-图标@3x.png')"
              alt="应用服务"
              class="node-icon"
            />
            <span class="node-label" :class="{ 'alarm-text': app.alarmCount > 0 }">
              {{ app.name }}
            </span>
          </div>
          <!-- 应用服务层无数据占位 -->
          <div v-else class="no-data-layer-placeholder">
            <span class="placeholder-text">暂无应用服务</span>
          </div>
        </div>
      </div>

      <!-- 中间件层 -->
      <div class="topology-layer">
        <div class="layer-label">中间件层</div>
        <div class="layer-content">
          <div
            v-if="processedData.middlewareLayer.length > 0"
            v-for="(middleware, index) in processedData.middlewareLayer"
            :key="`middleware-${index}`"
            class="topology-node"

            @mouseenter="showTooltip($event, middleware)"
            @mouseleave="hideTooltip"
          >
            <img
              :src="getIconPath('中间件-图标@3x.png')"
              alt="中间件"
              class="node-icon"
            />
            <span class="node-label" :class="{ 'alarm-text': middleware.alarmCount > 0 }">
              {{ middleware.name }}
            </span>
          </div>
          <!-- 中间件层无数据占位 -->
          <div v-else class="no-data-layer-placeholder">
            <span class="placeholder-text">暂无中间件</span>
          </div>
        </div>
      </div>

      <!-- 基础设施层 -->
      <div class="topology-layer">
        <div class="layer-label">基础设施层</div>
        <div class="layer-content">
          <div
            v-if="processedData.infrastructureLayer.length > 0"
            v-for="(infra, index) in processedData.infrastructureLayer"
            :key="`infra-${index}`"
            class="topology-node"

            @mouseenter="showTooltip($event, infra)"
            @mouseleave="hideTooltip"
          >
            <img
              :src="getIconPath('云平台-图标@3x.png')"
              alt="基础设施"
              class="node-icon"
            />
            <span class="node-label" :class="{ 'alarm-text': infra.alarmCount > 0 }">
              {{ infra.name }}
            </span>
          </div>
          <!-- 基础设施层无数据占位 -->
          <div v-else class="no-data-layer-placeholder">
            <span class="placeholder-text">暂无基础设施</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮提示框 -->
    <div
      v-if="tooltip.visible"
      class="tooltip"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      <div class="tooltip-content">
        <div>告警数量: {{ tooltip.data.alarmCount }}</div>
        <div>设备数量: {{ tooltip.data.count }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "SystemTopology"
});
</script>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from "vue";
import {
  getResAndAlarmCntGroupByRoomName,
  type ResAndAlarmCntParams,
  type ResAndAlarmCntResponse,
  type DeviceDetail
} from "@/api/statistics";

// 定义数据类型
interface TopologyNode {
  name: string;
  icon?: string;
  count?: number;
  alarmCount?: number;
}

interface TopologyData {
  applicationLayer: TopologyNode[];
  middlewareLayer: TopologyNode[];
  infrastructureLayer: TopologyNode[];
}

// 定义组件属性
interface Props {
  selectedRoom?: string;  // 选中的机房ID
  roomOptions?: Array<{ value: string; label: string }>;  // 机房选项列表，用于ID到名称的转换
  timeRange?: { days: number; type: string; customTimeRange?: { startTime: string; endTime: string } };
  loading?: boolean;
  error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  selectedRoom: "",
  roomOptions: () => [],
  timeRange: () => ({ days: 1, type: "day" }),
  loading: false,
  error: null
});

// 定义事件
const emit = defineEmits<{
  'update:loading': [loading: boolean];
  'update:error': [error: string | null];
}>();

// 响应式数据
const topologyData = ref<TopologyData>({
  applicationLayer: [],
  middlewareLayer: [],
  infrastructureLayer: []
});

const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  data: { alarmCount: 0, count: 0 }
});

// 通用时间格式化函数 - 精确到秒级
const formatDateToSeconds = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化时间范围参数用于接口调用
const formatTimeRangeForAPI = (timeRange: {
  days: number;
  customTimeRange?: { startTime: string; endTime: string }
}) => {
  // 如果有自定义时间范围，直接使用
  if (timeRange.customTimeRange) {
    return {
      startTime: timeRange.customTimeRange.startTime,
      endTime: timeRange.customTimeRange.endTime
    };
  }

  // 否则使用默认的天数计算
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - timeRange.days);

  return {
    startTime: formatDateToSeconds(startDate),
    endTime: formatDateToSeconds(endDate)
  };
};

// 获取图标路径的辅助函数
const getIconPath = (iconName: string) => {
  try {
    return new URL(`../assets/${iconName}`, import.meta.url).href;
  } catch {
    return "";
  }
};

// 根据设备类型获取对应图标
const getDeviceIcon = (deviceType: string): string => {
  const iconMap: Record<string, string> = {
    // 基础设施层图标
    "操作系统": "服务器-图标@3x.png",
    "网络设备": "网络设备-图标@3x.png",
    "云平台": "云平台-图标@3x.png",
    // 中间件层图标
    "中间件": "中间件-图标@3x.png",
    "数据库": "数据库-图标@3x.png",
    "金仓": "数据库-图标@3x.png",
    "Zookeeper": "中间件-图标@3x.png",
    // 应用服务层图标
    "应用服务": "应用服务-图标@3x.png"
  };
  return iconMap[deviceType] || "应用服务-图标@3x.png"; // 默认使用应用服务图标
};

// 获取系统架构数据
const fetchTopologyData = async () => {
  const roomName = selectedRoomName.value;
  if (!props.selectedRoom || !roomName) {
    console.log("没有选中的机房或机房名称为空，无法获取系统架构数据");
    // 清空数据
    topologyData.value = {
      applicationLayer: [],
      middlewareLayer: [],
      infrastructureLayer: []
    };
    return;
  }

  emit('update:loading', true);
  emit('update:error', null);

  try {
    const timeParams = formatTimeRangeForAPI(props.timeRange);
    const params: ResAndAlarmCntParams = {
      startTime: timeParams.startTime,
      endTime: timeParams.endTime,
      params: {}
    };

    console.log(`获取系统架构数据，机房: ${roomName}，参数:`, params);
    const response = await getResAndAlarmCntGroupByRoomName(params);
    console.log("系统架构数据响应:", response);

    if (response.status === "0" && response.data) {
      processTopologyData(response.data, roomName);
    } else {
      throw new Error("系统架构数据格式错误");
    }
  } catch (err) {
    const errorMessage = `获取系统架构数据失败: ${err.message || err}`;
    emit('update:error', errorMessage);
    console.error("获取系统架构数据失败:", err);
  } finally {
    emit('update:loading', false);
  }
};

// 处理API返回的数据
const processTopologyData = (data: ResAndAlarmCntResponse['data'], roomName: string) => {
  // 查找当前选中机房的数据
  const roomData = data[roomName];

  if (!roomData) {
    console.warn("未找到选中机房的数据:", roomName, "可用机房:", Object.keys(data));
    topologyData.value = {
      applicationLayer: [],
      middlewareLayer: [],
      infrastructureLayer: []
    };
    return;
  }

  const layers = roomData.type || {};

  // 处理基础设施层
  const infrastructureLayer: TopologyNode[] = [];
  if (layers["基础设施层"]?.subType) {
    Object.entries(layers["基础设施层"].subType).forEach(([deviceType, deviceData]) => {
      if ('count' in deviceData && 'alarmCount' in deviceData) {
        infrastructureLayer.push({
          name: deviceType,
          count: deviceData.count,
          alarmCount: deviceData.alarmCount,
          icon: getDeviceIcon(deviceType)
        });
      }
    });
  }

  // 处理中间件层 - 修正数据结构处理
  const middlewareLayer: TopologyNode[] = [];
  if (layers["中间件层"]?.subType) {
    Object.entries(layers["中间件层"].subType).forEach(([subTypeName, subTypeData]) => {
      // 检查是否有viSubType（具体设备）
      if ('viSubType' in subTypeData && subTypeData.viSubType) {
        Object.entries(subTypeData.viSubType).forEach(([deviceName, deviceDetail]) => {
          middlewareLayer.push({
            name: deviceName,
            count: deviceDetail.count,
            alarmCount: deviceDetail.alarmCount,
            icon: getDeviceIcon(deviceName) // 使用具体设备名称获取图标
          });
        });
      }
    });
  }

  // 处理应用服务层 - 修改为显示viSubType下的具体设备名称
  const applicationLayer: TopologyNode[] = [];
  if (layers["应用服务层"]?.subType) {
    Object.entries(layers["应用服务层"].subType).forEach(([_, subTypeData]) => {
      // 检查是否有viSubType（具体设备）
      if ('viSubType' in subTypeData && subTypeData.viSubType) {
        Object.entries(subTypeData.viSubType).forEach(([deviceName, deviceDetail]) => {
          applicationLayer.push({
            name: deviceName,
            count: deviceDetail.count,
            alarmCount: deviceDetail.alarmCount,
            icon: getDeviceIcon(deviceName) // 使用具体设备名称获取图标
          });
        });
      }
    });
  }

  topologyData.value = {
    applicationLayer,
    middlewareLayer,
    infrastructureLayer
  };

  console.log("系统架构数据处理完成:", topologyData.value);
  console.log("各层数据统计:", {
    应用服务层: applicationLayer.length,
    中间件层: middlewareLayer.length,
    基础设施层: infrastructureLayer.length
  });
};

// 显示悬浮提示框
const showTooltip = (event: MouseEvent, nodeData: TopologyNode) => {
  tooltip.value = {
    visible: true,
    x: event.clientX + 10,
    y: event.clientY - 10,
    data: {
      alarmCount: nodeData.alarmCount || 0,
      count: nodeData.count || 0
    }
  };
};

// 隐藏悬浮提示框
const hideTooltip = () => {
  tooltip.value.visible = false;
};

// 计算属性
const processedData = computed(() => topologyData.value);
const hasData = computed(() => {
  // 只要有选中的机房就显示架构，即使某些层没有数据也要显示占位
  return !!props.selectedRoom && !!selectedRoomName.value;
});

// 获取当前选中机房的名称
const selectedRoomName = computed(() => {
  if (!props.selectedRoom || !props.roomOptions.length) {
    return "";
  }
  const roomOption = props.roomOptions.find(option => option.value === props.selectedRoom);
  return roomOption ? roomOption.label : "";
});

// 监听机房变化
watch(() => props.selectedRoom, async (newRoom, oldRoom) => {
  if (newRoom && newRoom !== oldRoom) {
    console.log("系统架构: 机房变化", { from: oldRoom, to: newRoom });
    await fetchTopologyData();
  }
}, { immediate: true });

// 监听时间范围变化
watch(() => props.timeRange, async (newTimeRange, oldTimeRange) => {
  if (newTimeRange && props.selectedRoom && (
    !oldTimeRange ||
    newTimeRange.days !== oldTimeRange.days ||
    newTimeRange.type !== oldTimeRange.type
  )) {
    console.log("系统架构: 时间范围变化", { from: oldTimeRange, to: newTimeRange });
    await fetchTopologyData();
  }
}, { deep: true });

// 组件挂载时加载数据
onMounted(async () => {
  if (props.selectedRoom) {
    console.log("系统架构组件挂载，开始加载数据");
    await fetchTopologyData();
  }
});
</script>

<style lang="scss" scoped>
.system-topology-container {
  width: 100%;
  margin-bottom: 10px;
  padding: 20px; // 增加内边距
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  gap: 8px; // 减少标题和内容之间的间距
  box-sizing: border-box;
  position: relative;

  .hot-apps-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    margin-top: -4px;
    .hot-apps-icon {
      font-size: 16px;
      margin-right: 6px;
      display: flex;
      div {
        height: 13px;
        width: 3px;
        background: #3296fb;
        border-radius: 3px;
        transform: rotate(16deg);
      }
    }

    .hot-apps-text {
      font-size: 14px;
      font-weight: 600;
      color: #000000db;
    }
  }

  // 加载状态
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    min-height: 120px;

    .loading-spinner {
      font-size: 14px;
      color: #666666;
    }
  }

  // 暂无数据占位样式
  .no-data-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    min-height: 120px;

    .no-data-icon {
      margin-bottom: 12px;
      opacity: 0.6;

      svg {
        display: block;
      }
    }

    .no-data-text {
      font-size: 16px;
      color: #999999;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .no-data-desc {
      font-size: 12px;
      color: #cccccc;
      line-height: 1.4;
    }
  }
}

.topology-layer {
  display: flex;
  align-items: flex-start; // 改为顶部对齐，支持内容换行
  gap: 20px;
  margin-bottom: 16px; // 添加层级之间的间距

  &:last-child {
    margin-bottom: 0; // 最后一个层级不需要底部间距
  }

  .layer-label {
    width: 120px;
    height: 73px;
    background: rgb(231,242,255);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    color: #1976d2;
    border: 1px dashed #90caf9;
    flex-shrink: 0;
  }

  .layer-content {
    flex: 1;
    display: flex;
    align-items: center; // 垂直居中对齐
    gap: 16px; // 设备之间的间距
    flex-wrap: wrap; // 允许换行
    box-sizing: border-box;
    padding: 15px; // 增加内边距
    border: 1px solid #e4e7ed;
    border-style: dashed;
    border-radius: 6px;
    justify-content: space-around; // 分散对齐，设备之间均匀分布
    min-height: 80px; // 确保有固定高度
    align-content: center; // 多行内容居中对齐
  }
}

// 无数据层级占位样式
.no-data-layer-placeholder {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 14px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;

  .placeholder-text {
    color: #999999;
    font-style: italic;
  }
}

.topology-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  max-width: 120px; // 限制最大宽度，确保换行效果
  cursor: pointer;
  transition: transform 0.2s ease;
  margin: 4px 8px; // 调整外边距，适配分散布局

  &:hover {
    transform: translateY(-2px);
  }

  .node-icon {
    width: 33px;
    height: 33px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  .node-label {
    font-size: 12px;
    color: #2c3e50;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    word-break: break-word; // 长文本自动换行
    max-width: 100%; // 确保文本不超出容器
    transition: color 0.2s ease; // 颜色变化动画

    // 告警状态文字样式
    &.alarm-text {
      color: #e53e3e; // 红色告警文字
      font-weight: 600; // 加粗显示
    }
  }

  .node-count {
    font-size: 11px;
    color: #666;
    font-weight: 400;
  }
}

// 悬浮提示框样式
.tooltip {
  position: fixed;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .tooltip-content {
    div {
      margin: 2px 0;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .topology-layer {
    gap: 16px;
    margin-bottom: 14px; // 调整层级间距

    .layer-label {
      width: 100px;
      font-size: 13px;
    }

    .layer-content {
      gap: 12px; // 调整设备间距
      padding: 12px; // 调整内边距
      justify-content: space-around; // 保持分散对齐
    }
  }

  .topology-node {
    min-width: 70px;
    max-width: 100px; // 调整最大宽度
    margin: 3px 6px; // 调整外边距适配分散布局

    .node-icon {
      width: 30px; // 稍微减小图标尺寸
      height: 30px;
    }

    .node-label {
      font-size: 11px;
    }
  }
}

@media (max-width: 768px) {
  .system-topology-container {
    padding: 16px;
    gap: 12px;
  }

  .topology-layer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px; // 移动端增加层级间距

    .layer-label {
      width: 100%;
      height: 40px; // 稍微增加高度
      font-size: 14px;
    }

    .layer-content {
      width: 100%;
      justify-content: space-around; // 移动端也使用分散对齐
      gap: 10px; // 设备间距
      padding: 12px;
    }
  }

  .topology-node {
    min-width: 80px;
    max-width: 90px;
    margin: 4px 6px; // 调整移动端外边距

    .node-icon {
      width: 28px;
      height: 28px;
    }

    .node-label {
      font-size: 11px;
    }
  }

  // 移动端无数据占位样式调整
  .no-data-layer-placeholder {
    height: 50px;
    font-size: 13px;
  }
}
</style>
