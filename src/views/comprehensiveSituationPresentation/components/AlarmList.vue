<template>
  <div class="alarm-list">
    <div class="hot-apps-title">
      <div class="hot-apps-icon">
        <div></div>
        <div style="margin-left: 3px"></div>
      </div>
      <span class="hot-apps-text">实时告警列表</span>
    </div>
    <div class="alarm-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">加载中...</div>
      </div>

      <!-- 错误状态或暂无数据状态 -->
      <div v-else-if="error || totalAlarms === 0" class="no-data-placeholder">
        <div class="no-data-icon">
          <svg viewBox="0 0 24 24" width="48" height="48" fill="#d9d9d9">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <div class="no-data-text">暂无数据</div>
        <div class="no-data-desc">当前时间范围内没有相关数据</div>
      </div>

      <!-- 正常数据显示 -->
      <div v-else class="alarm-data-content">
        <div class="alarm-header">
          <div class="alarm-summary">
            <span>全部: {{ totalAlarms }}</span>
            <span class="severe">
              <img :src="severeIcon" alt="严重" />
              严重: {{ severeCount }}
            </span>
            <span class="important">
              <img :src="importantIcon" alt="重要" />
              重要: {{ importantCount }}
            </span>
            <span class="minor">
              <img :src="minorIcon" alt="一般" />
              一般: {{ minorCount }}
            </span>
            <span class="hint">
              <img :src="minorIcon" alt="提示" />
              提示: {{ hintCount }}
            </span>
          </div>
        </div>
        <div class="alarm-table-container">
          <div class="table-wrapper" :class="{ 'page-loading': pageLoading }">
            <el-table
              ref="alarmTableRef"
              :data="displayedAlarms"
              stripe
              size="small"
              :height="tableHeight"
              style="width: 100%"
              :header-cell-style="headerCellStyle"
              :cell-style="cellStyle"
              :row-style="rowStyle"
              empty-text="暂无告警数据"
              v-loading="pageLoading"
              element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
            >
            <el-table-column
              prop="id"
              label="序号"
              width="80"
              align="center"
              fixed
            />
            <el-table-column
              prop="time"
              label="最近发生事件"
              width="180"
              show-overflow-tooltip
            />
            <el-table-column
              prop="category"
              label="告警类别"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="level"
              label="告警级别"
              width="120"
              align="center"
            >
              <template #default="scope">
                <span :class="getLevelClass(scope.row.level)">
                  {{ scope.row.level }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="description"
              label="故障正文"
              min-width="200"
              show-overflow-tooltip
            />
          </el-table>
          </div>
        </div>
        <div class="alarm-pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20]"
            :pager-count="5"
            layout="sizes, prev, pager, next, jumper, total"
            :total="totalElements"
            background
            class="is-dark"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from "vue";
import severeIcon from "@/views/comprehensiveSituationPresentation/assets/严重告警-图标@3x.png";
import importantIcon from "@/views/comprehensiveSituationPresentation/assets/重要告警-图标@3x(1).png";
import minorIcon from "@/views/comprehensiveSituationPresentation/assets/轻微告警-图标@3x.png";
import {
  getAlarmDetailPage,
  getAlarmCountGroupBySeverity,
  type AlarmDetailItem,
  type AlarmDetailPageParams,
  type AlarmCountParams
} from "@/api/statistics";

// 定义组件接收的属性
interface Props {
  timeRange?: { days: number; type: string };
  defaultPageSize?: number;
  defaultCurrentPage?: number;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  timeRange: () => ({ days: 7, type: "week" }),
  defaultPageSize: 5,
  defaultCurrentPage: 1
});

// 定义事件
const emit = defineEmits<{
  'update:loading': [loading: boolean];
  'update:error': [error: string | null];
}>();

// 响应式数据
const loading = ref(false);
const pageLoading = ref(false); // 分页加载状态
const error = ref<string | null>(null);
const alarmList = ref<AlarmDetailItem[]>([]);
const totalElements = ref(0);
const currentPage = ref(props.defaultCurrentPage);
const pageSize = ref(props.defaultPageSize);

// 告警统计数据
const alarmCounts = ref({
  total: 0,
  severe: 0,    // 严重 (5)
  important: 0, // 重要 (4)
  minor: 0,     // 一般 (3)
  hint: 0       // 提示 (2)
});

// 通用时间格式化函数 - 精确到秒级
const formatDateToSeconds = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化时间范围参数用于接口调用 - 精确到秒级，支持自定义时间
const formatTimeRangeForAPI = (timeRange: {
  days: number;
  customTimeRange?: { startTime: string; endTime: string }
}) => {
  // 如果有自定义时间范围，直接使用
  if (timeRange.customTimeRange) {
    return {
      startTime: timeRange.customTimeRange.startTime,
      endTime: timeRange.customTimeRange.endTime
    };
  }

  // 否则使用默认的天数计算
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - timeRange.days);

  return {
    startTime: formatDateToSeconds(startDate),
    endTime: formatDateToSeconds(endDate)
  };
};

// 严重级别映射
const severityMap = {
  2: "提示",
  3: "一般",
  4: "重要",
  5: "严重"
};

// 根据告警级别获取对应的CSS类名
const getLevelClass = (level: string): string => {
  switch (level) {
    case "严重":
      return "level-severe";
    case "重要":
      return "level-important";
    case "一般":
      return "level-minor";
    case "提示":
      return "level-hint";
    default:
      return "";
  }
};

// 获取告警统计数据
const fetchAlarmCounts = async () => {
  try {
    const timeParams = formatTimeRangeForAPI(props.timeRange);
    const params: AlarmCountParams = {
      startTime: timeParams.startTime,
      endTime: timeParams.endTime,
      params: {}
    };

    console.log("获取告警统计数据，参数:", params);
    const response = await getAlarmCountGroupBySeverity(params);
    console.log("告警统计数据响应:", response);

    if (response.status === "0" && response.data) {
      const data = response.data;
      alarmCounts.value = {
        severe: parseInt(data["5"] || "0"),
        important: parseInt(data["4"] || "0"),
        minor: parseInt(data["3"] || "0"),
        hint: parseInt(data["2"] || "0"),
        total: parseInt(data["5"] || "0") + parseInt(data["4"] || "0") + parseInt(data["3"] || "0") + parseInt(data["2"] || "0")
      };
      console.log("告警统计数据更新:", alarmCounts.value);
    } else {
      console.warn("告警统计接口返回数据格式不正确:", response);
      throw new Error("告警统计数据格式错误");
    }
  } catch (err) {
    console.error("获取告警统计数据失败:", err);
    throw err;
  }
};

// 获取告警列表数据
const fetchAlarmList = async () => {
  try {
    const timeParams = formatTimeRangeForAPI(props.timeRange);
    const params: AlarmDetailPageParams = {
      startTime: timeParams.startTime,
      endTime: timeParams.endTime,
      params: {
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }
    };

    console.log("获取告警列表数据，参数:", params);
    const response = await getAlarmDetailPage(params);
    console.log("告警列表数据响应:", response);

    if (response.status === "0" && response.data) {
      alarmList.value = response.data.list || [];
      totalElements.value = response.data.totalElements || 0;
      console.log("告警列表数据更新:", {
        list: alarmList.value,
        total: totalElements.value
      });
    } else {
      console.warn("告警列表接口返回数据格式不正确:", response);
      throw new Error("告警列表数据格式错误");
    }
  } catch (err) {
    console.error("获取告警列表数据失败:", err);
    throw err;
  }
};

// 模拟数据生成函数已移除，现在使用真实API接口

// 加载告警数据 - 支持初始加载和分页加载
const loadAlarmData = async (isPagination = false) => {
  // 如果是分页操作，使用分页加载状态；否则使用全局加载状态
  if (isPagination) {
    pageLoading.value = true;
  } else {
    loading.value = true;
    error.value = null;
    emit('update:loading', true);
    emit('update:error', null);
  }

  try {
    console.log(isPagination ? "分页加载告警数据" : "初始加载告警数据");

    // 调用真实接口
    if (isPagination) {
      // 分页时只获取列表数据
      await fetchAlarmList();
    } else {
      // 初始加载时并行获取统计数据和列表数据
      await Promise.all([
        fetchAlarmCounts(),
        fetchAlarmList()
      ]);
    }

    console.log("告警数据加载完成:", {
      isPagination,
      counts: alarmCounts.value,
      list: alarmList.value,
      total: totalElements.value
    });
  } catch (err) {
    const errorMessage = `获取告警数据失败: ${err.message || err}`;
    if (!isPagination) {
      error.value = errorMessage;
      emit('update:error', errorMessage);
    }
    // ElMessage.error("获取告警数据失败");
  } finally {
    if (isPagination) {
      pageLoading.value = false;
    } else {
      loading.value = false;
      emit('update:loading', false);
    }
  }
};

// 处理分页变化
const handlePageChange = async () => {
  console.log("分页变化:", { currentPage: currentPage.value, pageSize: pageSize.value });
  await loadAlarmData(true); // 传入 true 表示这是分页操作
};

// 计算属性 - 格式化的告警列表数据
const displayedAlarms = computed(() => {
  return alarmList.value.map((item, index) => ({
    id: ((currentPage.value - 1) * pageSize.value + index + 1).toString().padStart(2, '0'),
    time: item.lastOccurrence,
    category: item.alertType || "未知类型",
    level: severityMap[item.origSeverity] || "提示",
    description: item.description
  }));
});

// 计算属性 - 统计数据
const totalAlarms = computed(() => alarmCounts.value.total);
const severeCount = computed(() => alarmCounts.value.severe);
const importantCount = computed(() => alarmCounts.value.important);
const minorCount = computed(() => alarmCounts.value.minor);
const hintCount = computed(() => alarmCounts.value.hint);

// 计算表格高度 - 固定高度，不随页面大小变化
const tableHeight = computed(() => {
  // 固定高度：表头 40px + 10行数据 400px = 440px
  // 这样无论选择多少条每页，表格高度都保持一致
  return 216;
});

// 表格引用
const alarmTableRef = ref();

// 表格样式配置
const headerCellStyle = {
  backgroundColor: "#f5f7fa",
  color: "#2c3e50",
  fontSize: "12px",
  fontWeight: "500",
  padding: "8px 12px",
  borderBottom: "1px solid #e4e7ed"
};

const cellStyle = {
  color: "#2c3e50",
  fontSize: "12px",
  padding: "8px 12px",
  borderBottom: "1px solid #f0f0f0"
};

const rowStyle = ({ rowIndex }: { rowIndex: number }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? "#ffffff" : "#fafafa"
  };
};

// 监听时间范围变化
watch(() => props.timeRange, async (newTimeRange, oldTimeRange) => {
  if (newTimeRange && (
    !oldTimeRange ||
    newTimeRange.days !== oldTimeRange.days ||
    newTimeRange.type !== oldTimeRange.type
  )) {
    console.log("告警列表: 时间范围变化", { from: oldTimeRange, to: newTimeRange });
    // 重置到第一页
    currentPage.value = 1;
    await loadAlarmData(false); // 时间范围变化是初始加载，需要重新获取统计数据
  }
}, { deep: true });

// 监听分页变化
watch([currentPage, pageSize], async () => {
  await handlePageChange();
});

// 组件挂载时加载数据
onMounted(async () => {
  console.log("告警列表组件挂载，开始加载数据");
  await loadAlarmData(false); // 初始加载
});

// 导出组件名称
defineOptions({
  name: 'AlarmList'
});
</script>

<style lang="scss" scoped>
.alarm-list {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  // background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .hot-apps-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .hot-apps-icon {
      font-size: 16px;
      margin-right: 6px;
      display: flex;
      div {
        height: 13px;
        width: 3px;
        background: #3296fb;
        border-radius: 3px;
        transform: rotate(16deg);
      }
    }

    .hot-apps-text {
      font-size: 14px;
      padding-top: 2px;
      font-weight: 600;
      color: #000000db;
    }
  }
  .alarm-title {
    margin-bottom: 15px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .alarm-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  // 加载状态
  .loading-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;

    .loading-spinner {
      font-size: 14px;
    }
  }

  // 暂无数据占位样式
  .no-data-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    min-height: 120px;

    .no-data-icon {
      margin-bottom: 12px;
      opacity: 0.6;

      svg {
        display: block;
      }
    }

    .no-data-text {
      font-size: 16px;
      color: #999999;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .no-data-desc {
      font-size: 12px;
      color: #cccccc;
      line-height: 1.4;
    }
  }

  .alarm-data-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .alarm-header {
    margin-bottom: 8px;
  }

  .alarm-summary {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #2c3e50;

    span {
      margin-right: 15px;
      display: flex;
      align-items: center;

      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }

    .severe {
      color: #ff3838;
    }

    .important {
      color: #ff7716;
    }

    .minor {
      color: #e4c513;
    }

    .hint {
      color: #409eff;
    }
  }

  .alarm-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .table-wrapper {
    position: relative;
    flex: 1;

    &.page-loading {
      // 分页加载时的样式，可以添加一些视觉效果
      .el-table {
        transition: opacity 0.3s ease;
      }
    }
  }

  // Element Plus 表格样式优化
  :deep(.el-table) {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;

    // 表头样式
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f5f7fa;
          border-bottom: 1px solid #e4e7ed;

          .cell {
            font-weight: 500;
            color: #2c3e50;
            line-height: 1.5;
          }
        }
      }
    }

    // 表体样式
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover > td {
            background-color: rgba(64, 158, 255, 0.1) !important;
          }

          td {
            border-bottom: 1px solid #f0f0f0;

            .cell {
              line-height: 1.5;
              word-break: break-all;
            }
          }
        }
      }
    }

    // 空数据样式
    .el-table__empty-block {
      .el-table__empty-text {
        color: #666666;
        font-size: 14px;
      }
    }

    // 滚动条样式
    .el-scrollbar {
      .el-scrollbar__bar {
        &.is-vertical {
          right: 2px;
          width: 6px;

          .el-scrollbar__thumb {
            background-color: rgba(144, 147, 153, 0.3);
            border-radius: 3px;

            &:hover {
              background-color: rgba(144, 147, 153, 0.5);
            }
          }
        }

        &.is-horizontal {
          display: none; // 隐藏水平滚动条
        }
      }
    }
  }

  // 告警级别样式
  .level-severe {
    color: #ff3838;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(255, 56, 56, 0.1);
  }

  .level-important {
    color: #ff7716;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(255, 119, 22, 0.1);
  }

  .level-minor {
    color: #e4c513;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(228, 197, 19, 0.1);
  }

  .level-hint {
    color: #409eff;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(64, 158, 255, 0.1);
  }

  .alarm-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #333333;

    // 使用Element Plus的默认样式
  }
}
</style>
