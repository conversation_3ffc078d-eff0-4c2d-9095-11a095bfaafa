<script setup lang="ts">
import DocumentChart from "./DocumentChart.vue";
import { defineComponent, computed } from 'vue';
import userUsageTitleIcon from '@/views/comprehensiveSituationPresentation/assets/用户数-图标@3x.png';

defineComponent({
  name: 'UserUsageSituation'
});

// 定义数据项类型
interface DataItem {
  id: string;
  value: number;
  label: string;
  unit: string;
  icon?: string;
}

// 定义数据行类型
interface DataRow {
  id: string;
  icon?: string;
  items: DataItem[];
}

// 定义热门应用数据类型
interface HotAppItem {
  id: string;
  name: string;
  count: number;
  rank: number;
}

// 定义组件接收的属性
interface Props {
  rows?: DataRow[];
  hotApps?: HotAppItem[]; // 新增热门应用数据
  isShowMiniTitle?: boolean;
  documentChartData?: any; // 新增文档使用数据
  title?: string;
  miniTitle?: string;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  rows: () => [],
  hotApps: () => [],
  isShowMiniTitle: true,
  documentChartData: undefined, // 改为 undefined，表示没有传递图表数据
  title: '用户使用态势',
  miniTitle: '热门应用TOP5'
});

// 获取图标路径
const getIconPath = (iconName: string) => {
  try {
    return new URL(`/src/views/comprehensiveSituationPresentation/assets/${iconName}`, import.meta.url).href;
  } catch (error) {
    console.error(`加载图标失败: ${iconName}`, error);
    return '';
  }
};

// 获取排名标签的样式类
const getRankClass = (rank: number) => {
  switch (rank) {
    case 1: return 'rank-top1';
    case 2: return 'rank-top2';
    case 3: return 'rank-top3';
    default: return 'rank-other';
  }
};

// 计算进度条的百分比
const getProgressPercent = (count: number, maxCount: number) => {
  return Math.round((count / maxCount) * 100);
};

// 获取最大数值用于计算进度条
const maxCount = computed(() => {
  if (props.hotApps.length === 0) return 1;
  return Math.max(...props.hotApps.map(app => app.count));
});
</script>

<template>
  <div class="user-usage-situation">
    <!-- 标题区域 -->
    <div class="title-section">
      <img :src="userUsageTitleIcon" :alt="props.title" class="title-icon" />
      <span class="title-text">{{props.title}}</span>
    </div>

    <!-- 用户数据统计区域 -->
    <div class="stats-section">
      <!-- 动态生成数据行 -->
      <div v-for="row in props.rows" :key="row.id" class="stats-row">
        <!-- 第一个数据项，带图标 -->
        <div v-if="row.items.length > 0" class="stats-item">
          <div v-if="row.icon" class="stats-icon">
            <img :src="getIconPath(row.icon)" :alt="row.items[0].label" />
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ row.items[0].value }} <span class="stats-unit">({{ row.items[0].unit }})</span></div>
            <div class="stats-label">{{ row.items[0].label }}</div>
          </div>
        </div>

        <!-- 其余数据项 -->
        <div v-for="(item, index) in row.items.slice(1)" :key="item.id" class="stats-item">
          <div class="stats-content">
            <div class="stats-value">{{ item.value }} <span class="stats-unit">({{ item.unit }})</span></div>
            <div class="stats-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 柱状图区域 - 只有在明确传入图表数据时显示 -->
    <div v-if="props.documentChartData !== undefined" style="padding-bottom: 6px">
      <document-chart  :chart-data="props.documentChartData" />
    </div>
    <!-- 热门应用TOP5区域 -->
    <div class="hot-apps-section">
      <!-- 热门应用标题 -->
      <div class="hot-apps-title">
        <div class="hot-apps-icon">
          <div></div>
          <div style="margin-left: 3px;"></div>
        </div>
        <span class="hot-apps-text">{{props.miniTitle}}</span>
      </div>

      <!-- 热门应用排行榜 -->
      <div v-if="props.hotApps && props.hotApps.length > 0" class="hot-apps-list">
        <div v-for="app in props.hotApps" :key="app.id" class="hot-app-item">
          <!-- 左侧排名标签 -->
          <div class="rank-section">
            <div class="rank-badge" :class="getRankClass(app.rank)">
              Top{{ app.rank }}
            </div>
          </div>

          <!-- 中间应用名称和进度条区域（上下排列） -->
          <div class="middle-section">
            <!-- 应用名称 -->
            <div class="app-name">{{ app.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar" :class="getRankClass(app.rank)"
                   :style="{ width: getProgressPercent(app.count, maxCount) + '%' }">
              </div>
            </div>
          </div>

          <!-- 右侧数值区域 -->
          <div class="count-section">
            <div class="app-count">{{ app.count }}</div>
          </div>
        </div>
      </div>

      <!-- 暂无数据占位 -->
      <div v-else class="no-data-placeholder">
        <div class="no-data-icon">
          <svg viewBox="0 0 24 24" width="48" height="48" fill="#d9d9d9">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <div class="no-data-text">暂无数据</div>
        <div class="no-data-desc">当前时间范围内没有相关数据</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.user-usage-situation {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #ffffff;

  // 标题区域
  .title-section {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #000;
    }
  }

  // 统计数据区域
  .stats-section {
    margin-bottom: 13px;

    .stats-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      gap: 40px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .stats-item {
      display: flex;
      align-items: center;

      .stats-icon {
        margin-right: 12px;

        img {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }
      }

      .stats-content {
        display: flex;
        flex-direction: column;

        .stats-value {
          font-size: 18px;
          font-weight: bold;
          color: #3b3b3b;
          line-height: 1.2;

          .stats-unit {
            font-size: 14px;
            color: #666666;
            font-weight: normal;
            margin-left: 2px;
          }
        }

        .stats-label {
          font-size: 12px;
          color: #666666;
          margin-top: 2px;
          white-space: nowrap;
        }
      }
    }
  }

  // 热门应用区域
  .hot-apps-section {
    .hot-apps-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .hot-apps-icon {
        font-size: 16px;
        margin-right: 6px;
        display: flex;
        div{
          height: 13px;
          width: 3px;
          background: #3296fb;
          border-radius: 3px;
          transform: rotate(16deg);
        }
      }

      .hot-apps-text {
        font-size: 14px;
        font-weight: 600;
        color: #000000db;
      }
    }

    .hot-apps-list {
      .hot-app-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        // 左侧排名标签区域
        .rank-section {
          width: 60px;
          height: 26px;
          display: flex;
          justify-content: center;
          align-items: center;

          .rank-badge {
            width: 48px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;

            &.rank-top1 {
              background: #FF3838;
            }

            &.rank-top2 {
              background: #FF7716;
            }

            &.rank-top3 {
              background: #E4C513;
            }

            &.rank-other {
              background: #0095FF;
            }
          }
        }

        // 中间应用名称和进度条区域（上下排列）
        .middle-section {
          width: calc(100% - 136px);
          height: 26px;
          padding: 0 10px 0 20px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: space-between;

          .app-name {
            font-size: 14px;
            color: #2c3e50;
            line-height: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            padding-bottom: 4px;
          }

          .progress-container {
            width: 100%;
            height: 8px;
            position: relative;
            display: flex;
            align-items: center;
            margin-top: 4px;
            background: rgba(0, 0, 0, 0.1);

            .progress-bar {
              height: 100%;
              transition: width 0.3s ease;

              &.rank-top1 {
                background: #FF3838;
              }

              &.rank-top2 {
                background: #FF7716;
              }

              &.rank-top3 {
                background: #E4C513;
              }

              &.rank-other {
                background: #0095FF;
              }
            }
          }
        }

        // 右侧数值区域
        .count-section {
          width: 76px;
          height: 26px;
          display: flex;
          align-items: center;

          .app-count {
            font-size: 16px;
            font-weight: 500;
            color: #1976d2;
            line-height: 16px;
            // padding-left: 12px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            background: rgba(135, 206, 235, 0.05);
            justify-content: center;
            // 四个角的线条效果
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background:
                linear-gradient(to right, #87ceeb 8px, transparent 8px),
                linear-gradient(to right, #87ceeb 8px, transparent 8px),
                linear-gradient(to left, #87ceeb 8px, transparent 8px),
                linear-gradient(to left, #87ceeb 8px, transparent 8px),
                linear-gradient(to bottom, #87ceeb 8px, transparent 8px),
                linear-gradient(to bottom, #87ceeb 8px, transparent 8px),
                linear-gradient(to top, #87ceeb 8px, transparent 8px),
                linear-gradient(to top, #87ceeb 8px, transparent 8px);
              background-size:
                100% 2px,
                100% 2px,
                100% 2px,
                100% 2px,
                2px 100%,
                2px 100%,
                2px 100%,
                2px 100%;
              background-position:
                left top,
                left bottom,
                right top,
                right bottom,
                left top,
                right top,
                left bottom,
                right bottom;
              background-repeat: no-repeat;
              pointer-events: none;
            }
          }
        }
      }
    }

    // 暂无数据占位样式
    .no-data-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      text-align: center;
      min-height: 120px;

      .no-data-icon {
        margin-bottom: 12px;
        opacity: 0.6;

        svg {
          display: block;
        }
      }

      .no-data-text {
        font-size: 16px;
        color: #999999;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .no-data-desc {
        font-size: 12px;
        color: #cccccc;
        line-height: 1.4;
      }
    }
  }
}
</style>
