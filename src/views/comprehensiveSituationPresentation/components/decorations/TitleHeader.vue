<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import type { DateModelType } from 'element-plus';
import { useFullscreen } from '@vueuse/core';
import { useSettingStoreHook } from '@/store/modules/settings';
import { emitter } from '@/utils/mitt';
import { message } from '@/utils/message';

// 导入投屏模式图标
import projectionModeIcon from '@/assets/svg/full_screen.svg';
import exitProjectionModeIcon from '@/assets/svg/exit_screen.svg';

const props = defineProps<{
  title: string;
}>();

// 定义事件发射
const emit = defineEmits(['timeRangeChange']);

// 全屏功能
const { isFullscreen, toggle } = useFullscreen();
const pureSetting = useSettingStoreHook();

// 是否处于投屏模式
const isProjectionMode = ref(false);

// 切换投屏模式
function toggleProjectionMode() {
  // 切换全屏
  toggle();

  // 切换内容区全屏（隐藏侧边栏）
  pureSetting.hiddenSideBar
    ? pureSetting.changeSetting({ key: 'hiddenSideBar', value: false })
    : pureSetting.changeSetting({ key: 'hiddenSideBar', value: true });

  // 切换标签栏显示状态
  pureSetting.changeSetting({ key: 'hiddenTag', value: !pureSetting.hiddenTag });

  // 触发标签视图变化事件
  emitter.emit('tagViewsChange', pureSetting.hiddenTag as unknown as string);

  // 更新投屏模式状态
  isProjectionMode.value = !isProjectionMode.value;

  // 显示提示信息
  if (isProjectionMode.value) {
    message('提示：已进入投屏模式，再次点击按钮或刷新页面可退出！', {
      showClose: true,
      duration: 5000,
      type: 'success'
    });
  }
}

// 当前日期
const currentDate = ref<any>('');

// 快捷选项状态
const activeShortcut = ref('day'); // 默认选中今日

// 日期选择器值变化处理 - 支持精确到秒的时间选择
const handleDateChange = (val: DateModelType) => {
  if (val && Array.isArray(val) && val.length === 2) {
    currentDate.value = val;
    // 当用户自己选择日期时，取消快捷选项的active状态
    activeShortcut.value = '';

    // 计算日期范围的天数（精确到秒）
    const startDate = new Date(val[0]);
    const endDate = new Date(val[1]);
    const timeDiff = endDate.getTime() - startDate.getTime();
    const days = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 包含结束日期

    // 发射时间范围变化事件，传递精确的时间信息
    emit('timeRangeChange', days, 'custom', {
      startTime: val[0],
      endTime: val[1]
    });
    console.log('TitleHeader: 自定义日期时间范围，天数:', days, '开始时间:', val[0], '结束时间:', val[1]);
  }
};

// 快捷选项点击处理
const handleShortcutClick = (type: string) => {
  activeShortcut.value = type;

  const end = new Date();
  let start = new Date();
  let days = 7; // 默认7天

  switch (type) {
    case 'day':
      // 今日：从今天00:00:00到23:59:59
      start = new Date();
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      days = 1;
      break;
    case 'week':
      start = new Date(Date.now() - 3600 * 1000 * 24 * 7);
      days = 7;
      break;
    case 'month':
      start = new Date(Date.now() - 3600 * 1000 * 24 * 30);
      days = 30;
      break;
  }

  currentDate.value = [start, end];

  // 发射时间范围变化事件
  emit('timeRangeChange', days, type);
  console.log('TitleHeader: 时间范围切换到', type, '天数:', days);
};

onMounted(() => {
  // 设置默认日期范围为今日
  handleShortcutClick('day');
});
</script>

<template>
  <div class="title-header">
    <!-- 投屏模式按钮 -->
    <!-- <div class="fullscreen-btn" @click="toggleProjectionMode" :title="isProjectionMode ? '退出投屏模式' : '进入投屏模式'">
      <img v-if="isProjectionMode" :src="exitProjectionModeIcon" alt="退出投屏模式" />
      <img v-else :src="projectionModeIcon" alt="进入投屏模式" />
    </div> -->

    <!-- 主要内容区域 -->
    <div class="header-content">
      <!-- 快捷时间按钮 -->
      <div class="time-shortcuts">
        <el-button
          :type="activeShortcut === 'day' ? 'primary' : 'default'"
          @click="handleShortcutClick('day')"
          class="shortcut-button"
        >
          今日
        </el-button>
        <el-button
          :type="activeShortcut === 'week' ? 'primary' : 'default'"
          @click="handleShortcutClick('week')"
          class="shortcut-button"
        >
          近一周
        </el-button>
        <el-button
          :type="activeShortcut === 'month' ? 'primary' : 'default'"
          @click="handleShortcutClick('month')"
          class="shortcut-button"
        >
          近一月
        </el-button>
      </div>

      <!-- 时间选择器 -->
      <div class="time-selector">
        <span class="time-label">时间：</span>
        <el-date-picker
          v-model="currentDate"
          type="datetimerange"
          range-separator="-"
          start-placeholder="起始时间"
          end-placeholder="结束时间"
          :clearable="false"
          @change="handleDateChange"
          class="date-picker"
          size="default"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </div>

      <!-- 重置按钮 -->
      <div class="reset-button">
        <el-button
          type="default"
          @click="handleShortcutClick('day')"
          class="reset-btn"
          size="default"
        >
          重置
        </el-button>
      </div>


    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 投屏模式下的样式调整 */
:deep(:fullscreen) {
  width: 100vw !important;
  height: 100vh !important;
  overflow: auto;
  background-color: #f5f7fa;

  .title-header {
    position: relative;
    width: 100% !important;
  }

  .main-content {
    width: 100% !important;
    height: calc(100vh - 66px) !important;
    display: flex;
    flex-direction: column;
  }

  /* 确保所有子元素也能自适应 */
  * {
    max-width: 100%;
  }

  /* 投屏模式下的其他元素调整 */
  .time-info {
    transform: scale(0.93);
    right: 10px;
  }

  /* 投屏模式下的按钮样式 */
  .fullscreen-btn {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(64, 158, 255, 0.7);
    box-shadow: 0 0 12px rgba(64, 158, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: rgba(64, 158, 255, 1);
      box-shadow: 0 0 16px rgba(64, 158, 255, 0.3);
    }
  }

  /* 确保图表和其他组件自适应 */
  .el-row,
  .el-col,
  .chart-container,
  .card-panel {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 响应式布局调整 */
  @media screen and (max-width: 1200px) {
    .title-text {
      font-size: 28px;
      letter-spacing: 6px;
    }

    .time-info {
      transform: scale(0.85);
      right: 5px;
    }
  }

  @media screen and (max-width: 768px) {
    .title-text {
      font-size: 24px;
      letter-spacing: 4px;
    }

    .time-info {
      transform: scale(0.75);
      right: 0;
      gap: 5px;
    }

    .date-picker-container {
      padding: 2px 5px;
    }

    .date-picker-wrapper {
      width: 250px;
    }
  }
}

.title-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // width: 100%;
  height: 56px;
  // background: #ffffff;
  // border-bottom: 1px solid #e5e7eb;
  // padding: 0 20px;
  box-sizing: border-box;

  .header-content {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    justify-content: flex-start;
    margin-left: 16px; // 为投屏按钮留出空间
  }

  .fullscreen-btn {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(64, 158, 255, 0.5);
    border-radius: 4px;
    transition: all 0.3s;
    z-index: 10;
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: rgba(64, 158, 255, 0.8);
      box-shadow: 0 0 12px rgba(64, 158, 255, 0.3);
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
    }

    img {
      width: 20px;
      height: 20px;
      transition: all 0.3s;
    }

    &:hover img {
      transform: scale(1.1);
    }

    @media screen and (max-width: 768px) {
      left: 10px;
      width: 32px;
      height: 32px;

      img {
        width: 18px;
        height: 18px;
      }
    }
  }

  .time-shortcuts {
    display: flex;
    gap: 0px;

    .shortcut-button {
      height: 30px;
      padding: 0 16px;
      font-size: 14px;
      border-radius: 4px;

      &.el-button--default {
        background: #ffffff;
        border-color: #d1d5db;
        color: #6b7280;

        &:hover {
          background: #f9fafb;
          border-color: #3b82f6;
          color: #3b82f6;
        }
      }

      &.el-button--primary {
        background: #3b82f6;
        border-color: #3b82f6;
        color: #ffffff;

        &:hover {
          background: #2563eb;
          border-color: #2563eb;
        }
      }
    }
  }

  .time-selector {
    display: flex;
    align-items: center;
    gap: 8px;

    .time-label {
      color: #374151;
      font-size: 14px;
      font-weight: 500;
    }

    .date-picker {
      width: 280px;

      :deep(.el-input__wrapper) {
        background: #ffffff;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        box-shadow: none;

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      :deep(.el-input__inner) {
        color: #374151;
        font-size: 14px;
      }

      :deep(.el-range-separator) {
        color: #6b7280;
      }

      :deep(.el-input__suffix) {
        .el-icon {
          color: #6b7280;
        }
      }
    }
  }

  .reset-button {
    display: flex;
    align-items: center;

    .reset-btn {
      height: 30px;
      padding: 0 16px;
      font-size: 14px;
      border-radius: 4px;
      background: #ffffff;
      border-color: #d1d5db;
      color: #6b7280;

      &:hover {
        background: #f9fafb;
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:active {
        background: #f3f4f6;
        border-color: #2563eb;
        color: #2563eb;
      }
    }
  }

}




// 简化的日期选择器样式
:deep(.el-picker__popper) {
  --el-bg-color: #ffffff;
  --el-border-color-light: #d1d5db;
  --el-text-color-regular: #374151;
  --el-color-primary: #3b82f6;

  border: 1px solid #d1d5db !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;

  .el-picker-panel {
    background-color: #ffffff;
    border: none;
    color: #374151;
  }

}
</style>
