<script setup lang="ts">
import { defineComponent, ref, onMounted, watch, onUnmounted, computed, type Ref } from 'vue';
import { delay, useDark, useECharts, type EchartOptions, type UtilsEChartsOption } from "@pureadmin/utils";
import { useAppStoreHook } from "@/store/modules/app";

defineComponent({
  name: 'ResourceLoadSituation'
});

// 定义组件属性
const props = withDefaults(defineProps<{
  // 资源数据数组，必须传入
  items: any[];
}>(), {
  items: () => []
});

// 主题相关
const { isDark } = useDark();
const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

// 响应式相关
const sideBarStatus = computed(() => {
  return useAppStoreHook().getSidebarStatus;
});
const layout = computed(() => {
  return useAppStoreHook().getLayout;
});

// 图表引用和实例
const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize, getInstance } = useECharts(
  chartRef as Ref<HTMLDivElement>,
  {
    theme
  }
);

// 图表配置
const getChartOption = () => {
  // 按数值从高到低排序，然后反转数组，让数值大的显示在上方
  const sortedItems = [...props.items].sort((a, b) => a.value - b.value);

  const categories = sortedItems.map(item => item.label);
  const totalData = sortedItems.map(item => item.value);

  // 根据参考图定义颜色映射，按照图中从上到下的顺序
  const getColorByLabel = (label: string, index: number) => {
    // 根据资源类型名称匹配颜色，严格按照参考图的颜色
    const colorMap: Record<string, string> = {
      '操作系统': '#E74C3C', // 红色 (最上方) - 更接近参考图的红色
      '数据库': '#F39C12',   // 橙色 (第二) - 更接近参考图的橙色
      '中间件': '#F1C40F',   // 黄色 (第三) - 更接近参考图的黄色
      '网络设备': '#3498DB', // 蓝色 (第四) - 更接近参考图的蓝色
      '应用服务': '#1ABC9C', // 青色 (第五) - 更接近参考图的青色
      '云平台': '#9B59B6'    // 紫色 (最下方) - 保持紫色
    };

    // 如果找到匹配的标签，返回对应颜色，否则使用默认颜色数组
    if (colorMap[label]) {
      return colorMap[label];
    }

    // 备用颜色数组，使用更准确的颜色
    const defaultColors = [
      '#E74C3C', '#F39C12', '#F1C40F', '#3498DB',
      '#1ABC9C', '#9B59B6', '#34495E', '#95A5A6'
    ];
    return defaultColors[index % defaultColors.length];
  };

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const dataIndex = params[0].dataIndex;
        const item = sortedItems[dataIndex];
        const usageRate = item.value > 0 ? ((item.onlineCount || 0) / item.value * 100).toFixed(1) : '0.0';
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${item.label}</div>
            <div>全部数量: ${item.value}</div>
            <div>在线数量: ${item.onlineCount || 0}</div>
            <div>在线率: ${usageRate}%</div>
          </div>
        `;
      }
    },
    grid: {
      left: '3%',
      right: '25%',
      bottom: '3%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      axisLabel: {
        color: '#666666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        color: '#666666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    series: [
      {
        name: '全部数量',
        type: 'bar',
        data: totalData.map((value, index) => ({
          value: value,
          itemStyle: {
            color: getColorByLabel(sortedItems[index].label, index),
            borderRadius: [0, 6, 6, 0] // 右侧圆角
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'right',
          formatter: function(params: any) {
            const dataIndex = params.dataIndex;
            const item = sortedItems[dataIndex];
            return `全部/在线: ${item.value}/${item.onlineCount || 0}`;
          },
          color: '#666666',
          fontSize: 12
        }
      }
    ]
  };
};

// 更新图表数据
const updateChart = () => {
  if (props.items.length === 0) return;

  const option = getChartOption();
  setOptions(option as UtilsEChartsOption);
};

// 监听数据变化
watch(() => props.items, () => {
  updateChart();
}, { deep: true });

// 监听主题变化
watch(theme, () => {
  delay(300).then(() => updateChart());
});

// 监听界面变化
watch([sideBarStatus, layout], () => {
  delay(600).then(() => resize());
});

// 组件挂载时初始化图表
onMounted(() => {
  delay(300).then(() => updateChart());
});

// 组件卸载时清理
onUnmounted(() => {
  const chart = getInstance();
  if (chart) {
    chart.dispose();
  }
});
</script>

<template>
  <div class="resource-load-situation">
    <!-- ECharts图表容器 -->
    <div v-if="props.items.length > 0" class="chart-container">
      <div ref="chartRef" class="chart"></div>
    </div>

    <!-- 暂无数据占位 -->
    <div v-else class="no-data-placeholder">
      <div class="no-data-icon">
        <svg viewBox="0 0 24 24" width="48" height="48" fill="#d9d9d9">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
      <div class="no-data-text">暂无数据</div>
      <div class="no-data-desc">当前时间范围内没有相关数据</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.resource-load-situation {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px 0;

  // 图表容器样式
  .chart-container {
    width: 100%;
    height: 300px; // 根据数据项数量可以调整高度

    .chart {
      width: 100%;
      height: 100%;
    }
  }

  // 暂无数据占位样式
  .no-data-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    min-height: 120px;

    .no-data-icon {
      margin-bottom: 12px;
      opacity: 0.6;

      svg {
        display: block;
      }
    }

    .no-data-text {
      font-size: 16px;
      color: #999999;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .no-data-desc {
      font-size: 12px;
      color: #cccccc;
      line-height: 1.4;
    }
  }
}
</style>
