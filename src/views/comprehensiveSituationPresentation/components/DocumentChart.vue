<template>
  <div class="document-chart-container">
    <!-- 图表区域 -->
    <div v-if="props.chartData && props.chartData.length > 0" ref="chartRef" class="document-chart"></div>

    <!-- 暂无数据占位 -->
    <div v-else class="no-data-placeholder">
      <div class="no-data-icon">
        <svg viewBox="0 0 24 24" width="48" height="48" fill="#d9d9d9">
          <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
        </svg>
      </div>
      <div class="no-data-text">暂无图表数据</div>
      <div class="no-data-desc">当前时间范围内没有统计数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, defineExpose } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  BarChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);

// 定义图表数据类型
interface ChartDataItem {
  date: string;
  documentProcessing: number;
  documentSeal: number;
  documentApplication?: number; // 添加第三个数据系列
}

const props = defineProps<{
  chartData: ChartDataItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 创建图表实例
  chartInstance = echarts.init(chartRef.value);

  // 设置响应式
  window.addEventListener('resize', handleResize);

  // 渲染图表
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return;

  console.log('DocumentChart: 开始渲染图表，数据:', props.chartData);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const date = params[0].axisValue;
        let html = `<div style="margin: 0px 0 0; line-height: 1;">${date}</div>`;
        params.forEach((item: any) => {
          html += `<div style="margin: 10px 0 0; line-height: 1;">
            <div style="margin: 0px 0 0; line-height: 1;">
              <div style="margin: 0px 0 0; line-height: 1;">
                <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span>
                ${item.seriesName}：${item.value} 次
              </div>
            </div>
          </div>`;
        });
        return `<div style="padding: 8px; border-radius: 4px; background: rgba(255, 255, 255, 0.95); color: #333; font-size: 12px; border: 1px solid #e4e7ed;">${html}</div>`;
      }
    },
    legend: {
      data: ['电子公文处理', '电子印章使用', '电子公文应用'],
      textStyle: {
        color: '#2c3e50',
        fontSize: 10
      },
      itemWidth: 12,
      itemHeight: 8,
      top: 0,
      right: 10
    },
    grid: {
      left: '8%',    // 增加左边距，为Y轴标签留出空间
      right: '5%',   // 增加右边距
      bottom: '8%',  // 增加底边距，为X轴标签留出空间
      top: '35px',   // 增加顶边距，为图例留出空间
      containLabel: true
    },
    backgroundColor: 'transparent',
    xAxis: {
      type: 'category',
      data: props.chartData.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#2c3e50',
        fontSize: 12,
        interval: 'auto', // 自动计算标签间隔，避免重叠
        rotate: 0,        // 不旋转标签
        margin: 8         // 标签与轴线的距离
      },
      axisTick: {
        show: true,       // 显示刻度线
        alignWithLabel: true, // 刻度线与标签对齐
        length: 4
      },
      boundaryGap: true   // 在柱子两侧留出空隙
    },
    yAxis: {
      type: 'value',
      name: '',
      min: 0,
      max: function(value: { max: number }) {
        return Math.ceil(value.max / 100) * 100;
      },
      interval: 100,
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#2c3e50',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          color: '#e4e7ed',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '电子公文处理',
        type: 'bar',
        barWidth: '20%', // 使用百分比，让ECharts自动计算合适的宽度
        barGap: '10%',   // 同一类别内柱子间的间距
        barCategoryGap: '30%', // 不同类别间的间距
        data: props.chartData.map(item => item.documentProcessing),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00BFFF' },
            { offset: 1, color: '#0080FF' }
          ])
        }
      },
      {
        name: '电子印章使用',
        type: 'bar',
        barWidth: '20%',
        barGap: '10%',
        barCategoryGap: '30%',
        data: props.chartData.map(item => item.documentSeal),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00FF7F' },
            { offset: 1, color: '#00CD66' }
          ])
        }
      },
      {
        name: '电子公文应用',
        type: 'bar',
        barWidth: '20%',
        barGap: '10%',
        barCategoryGap: '30%',
        data: props.chartData.map(item => item.documentApplication || 0),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#FFD700' },
            { offset: 1, color: '#FFA500' }
          ])
        }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 监听数据变化
watch(() => props.chartData, (newData, oldData) => {
  console.log('DocumentChart: 数据变化', { newData, oldData });
  updateChart();
}, { deep: true, immediate: true });

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

// 导出组件
defineExpose({
  updateChart
});
</script>

<style lang="scss" scoped>
.document-chart-container {
  width: 100%;
  height: 180px;
  position: relative;
}

.document-chart {
  width: 100%;
  height: 100%;
}

.no-data-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  .no-data-icon {
    margin-bottom: 12px;
    opacity: 0.6;

    svg {
      display: block;
    }
  }

  .no-data-text {
    font-size: 16px;
    color: #999999;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .no-data-desc {
    font-size: 12px;
    color: #cccccc;
    line-height: 1.4;
  }
}
</style>
