<script setup lang="ts">
import {
  ref,
  onMounted,
  watch,
  reactive,
  toRef,
  nextTick,
  computed,
  watchEffect,
  readonly
} from "vue";
// 使用CSS实现响应式而不是动态绑定
import "./assets/responsive.css";
import { ElMessage } from "element-plus";
import BorderBox from "./components/decorations/BorderBox.vue";
import BackgroundBox from "./components/decorations/BackgroundBox.vue";
import TitleHeader from "./components/decorations/TitleHeader.vue";
import UserUsageSituation from "./components/UserUsageSituation.vue";
import ResourceLoadSituation from "./components/ResourceLoadSituation.vue";
import DataCenterMonitor from "./components/DataCenterMonitor.vue";
import {
  userOverviewUserCount,
  userOverviewUsage,
  userOverviewHotApp,
  docAppTotalStats,
  docAppTimeStats,
  docAppDeptTop5,
  resourceOverview,
  resourceCountByType,
  resourceRoomList,
  resourceCountByRoom,
  getPerUsageByRoomName,
  getPerTrendByRoomName,
  getAlarmCountAndDurBySeverity,
  getAlarmGroupByAndTop,
  alarmAppCount,
  type ResourceOverviewData,
  type ResourceTypeData,
  type ResourceRoomData,
  type ResourceCountByRoomParams,
  type ResourceCountByRoomData,
  type PerUsageByRoomNameParams,
  type PerUsageByRoomNameResponse,
  type PerTrendByRoomNameParams,
  type PerTrendByRoomNameResponse,
  type PerTrendDataItem,
  type AlarmCountAndDurParams,
  type AlarmCountAndDurResponse,
  type AlarmGroupByAndTopParams,
  type AlarmGroupByAndTopResponse,
  type AlarmAppCountParams,
  type AlarmAppCountResponse
} from "@/api/statistics";

// 定义API响应的类型接口
interface ApiResponse {
  status: string;
  data: any;
  message?: string;
}

// 定义热门应用数据类型
interface HotAppData {
  log_info: string;
  total: string;
}

// 定义公文应用统计数据类型
interface DocAppTotalData {
  totalProcess: number;
  totalSeal: number;
  totalApp: number;
}

// 定义时间统计数据类型
interface TimeStatsData {
  statDate: string;
  totalProcess: string;
  totalSeal: string;
  totalApp: string;
}

// 定义部门TOP5数据类型
interface DeptTop5Data {
  id: string | null;
  orgName: string;
  orgId: string;
  processCount: number;
  sealUsage: number;
  appCount: number;
  recordTime: string | null;
  total: number;
}

// 接口管理器相关类型定义
interface ApiInfo {
  name: string;
  fn: () => Promise<any>;
  critical: boolean;
}

interface ApiGroup {
  name: string;
  apis: ApiInfo[];
}

interface ApiResult {
  success: boolean;
  data?: any;
  error?: any;
  apiName: string;
  critical?: boolean;
}

interface GroupResult {
  groupName: string;
  total: number;
  success: number;
  failed: number;
  criticalFailures: number;
  results: PromiseSettledResult<ApiResult>[];
}
// ==================== 全新的数据管理系统 ====================

// 1. 数据状态枚举
enum DataState {
  IDLE = "idle",
  LOADING = "loading",
  SUCCESS = "success",
  ERROR = "error"
}

// 2. 用户态势数据管理器
class UserSituationManager {
  private state = reactive({
    // 数据状态
    dataState: DataState.IDLE,
    loadingState: {
      userCount: false,
      userUsage: false,
      hotApps: false
    },

    // 原始数据
    rawData: {
      userCount: null,
      userUsage: null,
      hotApps: null
    },

    // 处理后的数据
    processedData: {
      userStats: {
        totalUsers: 0,
        dailyActiveUsers: 0,
        monthlyActiveUsers: 0,
        dailyLogins: 0,
        monthlyLogins: 0
      },
      hotApps: []
    },

    // 错误信息
    errors: {
      userCount: null,
      userUsage: null,
      hotApps: null
    },

    // 更新时间戳
    lastUpdate: 0,
    version: 0
  });

  // 获取状态
  getState() {
    return readonly(this.state);
  }

  // 检查是否正在加载
  isLoading() {
    return Object.values(this.state.loadingState).some(loading => loading);
  }

  // 检查是否有数据
  hasData() {
    return (
      this.state.processedData.userStats.totalUsers > 0 ||
      this.state.processedData.hotApps.length > 0
    );
  }

  // 设置加载状态
  setLoading(type: "userCount" | "userUsage" | "hotApps", loading: boolean) {
    this.state.loadingState[type] = loading;
    this.updateDataState();
  }

  // 设置错误
  setError(type: "userCount" | "userUsage" | "hotApps", error: string | null) {
    this.state.errors[type] = error;
    this.updateDataState();
  }

  // 更新用户数量数据
  updateUserCount(data: any) {
    console.log("📊 更新用户数量数据:", data);

    this.state.rawData.userCount = data;
    this.state.processedData.userStats.totalUsers = data?.userCount || 0;
    this.state.processedData.userStats.dailyActiveUsers = data?.dayCount || 0;
    this.state.processedData.userStats.monthlyActiveUsers =
      data?.monthCount || 0;

    this.state.errors.userCount = null;
    this.updateVersion();
  }

  // 更新用户使用数据
  updateUserUsage(data: any) {
    console.log("📊 更新用户使用数据:", data);

    this.state.rawData.userUsage = data;
    this.state.processedData.userStats.dailyLogins = data?.dayCount || 0;
    this.state.processedData.userStats.monthlyLogins = data?.monthCount || 0;

    this.state.errors.userUsage = null;
    this.updateVersion();
  }

  // 更新热门应用数据
  updateHotApps(data: any[]) {
    console.log("📊 更新热门应用数据:", data);

    this.state.rawData.hotApps = data;

    // 处理热门应用数据
    const validApps = data.filter(
      app =>
        app &&
        typeof app === "object" &&
        app.log_info &&
        app.total !== undefined &&
        app.total !== null
    );

    if (validApps.length > 0) {
      this.state.processedData.hotApps = validApps
        .sort((a, b) => parseInt(b.total) - parseInt(a.total))
        .slice(0, 5)
        .map((app, index) => ({
          id: `app-${index + 1}`,
          name: app.log_info || "未知应用",
          count: parseInt(app.total) || 0,
          rank: index + 1
        }));
    } else {
      this.state.processedData.hotApps = [];
    }

    this.state.errors.hotApps = null;
    this.updateVersion();
  }

  // 清空所有数据
  clearData() {
    console.log("🧹 清空用户态势数据");

    this.state.rawData = {
      userCount: null,
      userUsage: null,
      hotApps: null
    };

    this.state.processedData = {
      userStats: {
        totalUsers: 0,
        dailyActiveUsers: 0,
        monthlyActiveUsers: 0,
        dailyLogins: 0,
        monthlyLogins: 0
      },
      hotApps: []
    };

    this.state.errors = {
      userCount: null,
      userUsage: null,
      hotApps: null
    };

    this.updateVersion();
  }

  // 更新数据状态
  private updateDataState() {
    if (this.isLoading()) {
      this.state.dataState = DataState.LOADING;
    } else if (Object.values(this.state.errors).some(error => error !== null)) {
      this.state.dataState = DataState.ERROR;
    } else if (this.hasData()) {
      this.state.dataState = DataState.SUCCESS;
    } else {
      this.state.dataState = DataState.IDLE;
    }
  }

  // 更新版本号
  private updateVersion() {
    this.state.version++;
    this.state.lastUpdate = Date.now();
    this.updateDataState();

    console.log("🔄 用户态势数据版本更新:", {
      version: this.state.version,
      dataState: this.state.dataState,
      userStats: this.state.processedData.userStats,
      hotAppsCount: this.state.processedData.hotApps.length
    });
  }
}

// 3. 创建用户态势管理器实例
const userSituationManager = new UserSituationManager();

// 4. 响应式数据引用
const userSituationState = userSituationManager.getState();

// 5. 计算属性：用户使用态势数据
const userUsageData = computed(() => ({
  rows: [
    {
      id: "user-row",
      icon: "用户数-图标@3x.png",
      items: [
        {
          id: "total-users",
          value: userSituationState.processedData.userStats.totalUsers,
          label: "用户总数",
          unit: "人"
        },
        {
          id: "daily-active-users",
          value: userSituationState.processedData.userStats.dailyActiveUsers,
          label: "日活用户数",
          unit: "人"
        },
        {
          id: "monthly-active-users",
          value: userSituationState.processedData.userStats.monthlyActiveUsers,
          label: "月活用户数",
          unit: "人"
        }
      ]
    },
    {
      id: "login-row",
      icon: "登录次数-图标@3x.png",
      items: [
        {
          id: "daily-logins",
          value: userSituationState.processedData.userStats.dailyLogins,
          label: "本日登录次数",
          unit: "次"
        },
        {
          id: "monthly-logins",
          value: userSituationState.processedData.userStats.monthlyLogins,
          label: "本月登录次数",
          unit: "次"
        }
      ]
    }
  ]
}));

// 6. 计算属性：热门应用数据
const hotAppsData = computed(() => userSituationState.processedData.hotApps);

// 部门TOP5数据 - 完全依赖接口数据
const deptTop5Data = ref([]);
// 系统架构拓扑图数据
const topologyData = ref({
  applicationLayer: [
    { name: "应用01" },
    { name: "应用02" },
    { name: "应用03" },
    { name: "应用04" },
    { name: "应用05" },
    { name: "应用06" }
  ],
  middlewareLayer: [{ name: "平台云0套" }],
  infrastructureLayer: [
    { name: "服务器", count: "0台", icon: "服务器-图标@3x.png" },
    { name: "虚拟机", count: "0台", icon: "虚拟机-图标@3x.png" }
  ]
});

// ==================== 全新的API调用系统 ====================

// 1. 用户态势数据获取器
class UserSituationDataFetcher {
  private manager: UserSituationManager;

  constructor(manager: UserSituationManager) {
    this.manager = manager;
  }

  // 获取完整的用户态势数据
  async fetchAllData(timeRange: { days: number } = { days: 7 }) {
    console.log("� 开始获取用户态势完整数据...");

    try {
      // 并行获取所有数据
      const [userCountResult, userUsageResult, hotAppsResult] =
        await Promise.allSettled([
          this.fetchUserCount(),
          this.fetchUserUsage(),
          this.fetchHotApps(timeRange.days)
        ]);

      // 处理结果
      let successCount = 0;
      let errorCount = 0;

      if (userCountResult.status === "fulfilled") {
        successCount++;
      } else {
        errorCount++;
        this.manager.setError(
          "userCount",
          userCountResult.reason?.message || "用户数量获取失败"
        );
      }

      if (userUsageResult.status === "fulfilled") {
        successCount++;
      } else {
        errorCount++;
        this.manager.setError(
          "userUsage",
          userUsageResult.reason?.message || "用户使用数据获取失败"
        );
      }

      if (hotAppsResult.status === "fulfilled") {
        successCount++;
      } else {
        errorCount++;
        this.manager.setError(
          "hotApps",
          hotAppsResult.reason?.message || "热门应用数据获取失败"
        );
      }

      console.log(
        `✅ 用户态势数据获取完成: ${successCount}/3 成功, ${errorCount}/3 失败`
      );

      return {
        success: successCount,
        total: 3,
        errors: errorCount
      };
    } catch (error) {
      console.error("💥 用户态势数据获取异常:", error);
      throw error;
    }
  }

  // 获取用户数量数据
  private async fetchUserCount() {
    console.log("📊 获取用户数量数据...");
    this.manager.setLoading("userCount", true);

    try {
      const response = await userOverviewUserCount();
      console.log("📊 用户数量原始数据:", response);

      const apiResponse = response as ApiResponse;
      if (apiResponse.status === "0" && apiResponse.data) {
        this.manager.updateUserCount(apiResponse.data);
        console.log("✅ 用户数量数据处理完成");
      } else {
        throw new Error("用户数量接口返回数据格式不正确");
      }
    } catch (error) {
      console.error("❌ 用户数量数据获取失败:", error);
      this.manager.setError("userCount", error.message || "获取失败");
      throw error;
    } finally {
      this.manager.setLoading("userCount", false);
    }
  }

  // 获取用户使用数据
  private async fetchUserUsage() {
    console.log("📊 获取用户使用数据...");
    this.manager.setLoading("userUsage", true);

    try {
      const response = await userOverviewUsage();
      console.log("📊 用户使用原始数据:", response);

      const apiResponse = response as ApiResponse;
      if (apiResponse.status === "0" && apiResponse.data) {
        this.manager.updateUserUsage(apiResponse.data);
        console.log("✅ 用户使用数据处理完成");
      } else {
        throw new Error("用户使用接口返回数据格式不正确");
      }
    } catch (error) {
      console.error("❌ 用户使用数据获取失败:", error);
      this.manager.setError("userUsage", error.message || "获取失败");
      throw error;
    } finally {
      this.manager.setLoading("userUsage", false);
    }
  }

  // 获取热门应用数据
  private async fetchHotApps(days: number) {
    console.log("📊 获取热门应用数据, 天数:", days);
    this.manager.setLoading("hotApps", true);

    try {
      const response = await userOverviewHotApp(days);
      console.log("📊 热门应用原始数据:", response);

      const apiResponse = response as ApiResponse;
      if (
        apiResponse.status === "0" &&
        apiResponse.data &&
        Array.isArray(apiResponse.data)
      ) {
        this.manager.updateHotApps(apiResponse.data);
        console.log("✅ 热门应用数据处理完成");
      } else {
        throw new Error("热门应用接口返回数据格式不正确");
      }
    } catch (error) {
      console.error("❌ 热门应用数据获取失败:", error);
      this.manager.setError("hotApps", error.message || "获取失败");
      throw error;
    } finally {
      this.manager.setLoading("hotApps", false);
    }
  }

  // 重新获取热门应用数据（用于时间范围切换）
  async refetchHotApps(days: number) {
    console.log("🔄 重新获取热门应用数据, 天数:", days);
    return this.fetchHotApps(days);
  }
}

// 2. 创建数据获取器实例
const userSituationDataFetcher = new UserSituationDataFetcher(
  userSituationManager
);

// ==================== 重写的数据获取函数 ====================

// 调用用户态势相关接口的函数 - 使用新的数据管理器
const fetchUserOverviewData = async () => {
  console.log("🚀 开始获取用户态势数据（使用新管理器）...");

  try {
    // 使用新的数据获取器
    const result = await userSituationDataFetcher.fetchAllData(
      currentTimeRange.value
    );

    // 更新旧的dataStatus以保持兼容性
    const state = userSituationManager.getState();
    dataStatus.value.userOverview.loading =
      state.loadingState.userCount || state.loadingState.userUsage;
    dataStatus.value.hotApps.loading = state.loadingState.hotApps;
    dataStatus.value.userOverview.hasData =
      state.processedData.userStats.totalUsers > 0;
    dataStatus.value.hotApps.hasData = state.processedData.hotApps.length > 0;
    dataStatus.value.userOverview.error =
      state.errors.userCount || state.errors.userUsage;
    dataStatus.value.hotApps.error = state.errors.hotApps;

    console.log("� 用户态势数据获取完成:", {
      success: result.success,
      total: result.total,
      errors: result.errors,
      dataState: state.dataState,
      userStats: state.processedData.userStats,
      hotAppsCount: state.processedData.hotApps.length
    });

    return result;
  } catch (error) {
    console.error("💥 获取用户态势数据失败:", error);
    // // ElMessage.error("获取用户态势数据失败");
    return null;
  }
};

// 调用公文应用态势相关接口的函数
const fetchDocAppData = async (days: number = 7) => {
  // 设置加载状态
  dataStatus.value.docApp.loading = true;
  dataStatus.value.documentChart.loading = true;
  dataStatus.value.deptTop5.loading = true;
  dataStatus.value.docApp.error = null;
  dataStatus.value.documentChart.error = null;
  dataStatus.value.deptTop5.error = null;

  try {
    console.log("开始获取公文应用态势数据...");

    // 并行调用所有接口以提高性能
    const [totalStatsRes, timeStatsRes, deptTop5Res] = await Promise.all([
      docAppTotalStats(days),
      docAppTimeStats(days),
      docAppDeptTop5(days)
    ]);

    console.log("公文应用总体统计数据:", totalStatsRes);
    console.log("公文应用时间统计数据:", timeStatsRes);
    console.log("公文应用部门TOP5数据:", deptTop5Res);

    // 处理公文应用态势数据
    const totalStatsResponse = totalStatsRes as ApiResponse;
    if (totalStatsResponse.status === "0" && totalStatsResponse.data) {
      const totalData = totalStatsResponse.data as DocAppTotalData;
      // 更新公文应用态势统计数据
      documentUsageData.value.rows[0].items[0].value =
        totalData.totalProcess || 0;
      documentUsageData.value.rows[1].items[0].value = totalData.totalSeal || 0;
      documentUsageData.value.rows[2].items[0].value = totalData.totalApp || 0;
      dataStatus.value.docApp.hasData = true;
    } else {
      console.warn(
        "公文应用总体统计接口返回数据格式不正确:",
        totalStatsResponse
      );
      dataStatus.value.docApp.error = "公文应用统计数据格式错误";
      // 清空数据
      documentUsageData.value.rows[0].items[0].value = 0;
      documentUsageData.value.rows[1].items[0].value = 0;
      documentUsageData.value.rows[2].items[0].value = 0;
    }

    // 处理时间统计数据，用于图表展示
    const timeStatsResponse = timeStatsRes as ApiResponse;
    if (
      timeStatsResponse.status === "0" &&
      timeStatsResponse.data &&
      Array.isArray(timeStatsResponse.data)
    ) {
      const timeData = timeStatsResponse.data as TimeStatsData[];

      // 验证数据有效性
      const validData = timeData.filter(
        item => item && typeof item === "object" && item.statDate
      );

      if (validData.length > 0) {
        // 转换为图表数据格式
        documentChartData.value = validData.map((item: TimeStatsData) => ({
          date: formatDateForChart(item.statDate, days),
          documentProcessing: parseInt(item.totalProcess) || 0,
          documentSeal: parseInt(item.totalSeal) || 0,
          documentApplication: parseInt(item.totalApp) || 0
        }));
        dataStatus.value.documentChart.hasData = true;
        console.log("图表数据转换完成:", documentChartData.value);
      } else {
        console.warn("获取到的时间统计数据无效，保持默认显示");
        // 保持默认数据，不清空
        dataStatus.value.documentChart.hasData = false;
      }
    } else {
      console.warn("未获取到有效的时间统计数据，保持默认显示");
      dataStatus.value.documentChart.error = "时间统计数据格式错误";
      // 保持默认数据，不清空
    }

    // 处理部门TOP5数据
    const deptTop5Response = deptTop5Res as ApiResponse;
    if (
      deptTop5Response.status === "0" &&
      deptTop5Response.data &&
      Array.isArray(deptTop5Response.data)
    ) {
      const deptData5 = deptTop5Response.data as DeptTop5Data[];

      console.log("原始部门Top5数据:", deptData5);

      // 数据验证和过滤
      const validDeptData = deptData5.filter(
        dept =>
          dept &&
          typeof dept === "object" &&
          dept.orgName &&
          dept.total !== undefined &&
          dept.total !== null
      );

      if (validDeptData.length > 0) {
        // 按照total字段降序排序，取前5个
        const sortedDepts = validDeptData
          .sort((a: DeptTop5Data, b: DeptTop5Data) => b.total - a.total)
          .slice(0, 5);

        // 计算百分比（以最高值为100%基准）
        const maxValue = sortedDepts.length > 0 ? sortedDepts[0].total : 0;

        // 为TableTop5组件准备数据
        deptData.value.data = sortedDepts.map(
          (dept: DeptTop5Data, index: number) => ({
            label: dept.orgName || "未知部门",
            value: dept.total || 0,
            percent:
              maxValue > 0 ? Math.round((dept.total / maxValue) * 100) : 0,
            rank: index + 1
          })
        );

        // 为UserUsageSituation组件准备符合HotAppItem接口的数据
        deptTop5Data.value = sortedDepts.map(
          (dept: DeptTop5Data, index: number) => ({
            id: `dept-${dept.orgId || index + 1}`,
            name: dept.orgName || "未知部门",
            count: dept.total || 0,
            rank: index + 1
          })
        );

        dataStatus.value.deptTop5.hasData = true;
        console.log("转换后的部门Top5数据:", {
          tableData: deptData.value.data,
          hotAppData: deptTop5Data.value
        });
      } else {
        console.warn("没有有效的部门Top5数据");
        deptData.value.data = [];
        deptTop5Data.value = [];
        dataStatus.value.deptTop5.hasData = false;
      }
    } else {
      console.warn("部门Top5接口返回数据格式不正确:", deptTop5Response);
      dataStatus.value.deptTop5.error = "部门Top5数据格式错误";
      deptData.value.data = [];
      deptTop5Data.value = [];
    }

    console.log("公文态势数据处理完成:", {
      documentUsageData: documentUsageData.value,
      documentChartData: documentChartData.value,
      deptData: deptData.value,
      dataStatus: dataStatus.value
    });

    return {
      totalStats: totalStatsRes,
      timeStats: timeStatsRes,
      deptTop5: deptTop5Res
    };
  } catch (error) {
    console.error("获取公文应用态势数据失败:", error);

    // 设置错误状态
    dataStatus.value.docApp.error = `接口调用失败: ${error.message || error}`;
    dataStatus.value.documentChart.error = `接口调用失败: ${error.message || error}`;
    dataStatus.value.deptTop5.error = `接口调用失败: ${error.message || error}`;

    // 清空统计数据，但保持图表默认显示
    documentUsageData.value.rows[0].items[0].value = 0;
    documentUsageData.value.rows[1].items[0].value = 0;
    documentUsageData.value.rows[2].items[0].value = 0;
    // 保持图表默认数据，不清空
    deptData.value.data = [];
    deptTop5Data.value = [];

    // // ElMessage.error("获取公文应用态势数据失败");
    return null;
  } finally {
    dataStatus.value.docApp.loading = false;
    dataStatus.value.documentChart.loading = false;
    dataStatus.value.deptTop5.loading = false;
  }
};

// 格式化日期用于图表显示
const formatDateForChart = (dateStr: string, days: number): string => {
  if (days === 1) {
    // 日视图：显示小时
    return dateStr + "时";
  } else {
    // 周/月视图：显示月/日格式
    const date = new Date(dateStr);
    return `${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
  }
};

// 根据设备类型获取对应图标
const getResourceIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    虚拟机: "vm-icon",
    中间件: "middleware-icon",
    服务器: "server-icon",
    应用服务: "app-service-icon",
    云平台: "cloud-platform-icon"
  };
  return iconMap[type] || "default-icon";
};

// 清空机房监控数据
const clearRoomMonitorData = () => {
  console.log("清空机房监控数据");

  // 重置机房信息为默认状态
  dataCenterMonitorData.value.dataCenterInfo = {
    name: "请选择机房",
    station: "暂无数据",
    area: "暂无数据"
  };

  // 重置统计数据
  dataCenterMonitorData.value.statistics = {
    cabinets: 0,
    devices: 0,
    alerts: 0
  };

  // 重置资源使用率
  dataCenterMonitorData.value.resourceUsage = {
    cpu: 0,
    memory: 0,
    disk: 0
  };

  // 清空图表数据
  dataCenterMonitorData.value.minuteData = [];
  dataCenterMonitorData.value.hourData = [];

  // 设置数据状态为无数据
  dataStatus.value.dataCenterMonitor.hasData = false;
  dataStatus.value.dataCenterMonitor.error = null;
};

// 获取机房性能趋势图表数据
const fetchRoomTrendData = async (
  roomId?: string,
  customTimeRange?: { days: number },
  timeType: "min" | "hour" = "min"
) => {
  try {
    // 确定要查询的机房
    const targetRoomId = roomId || selectedDataCenter.value;

    if (!targetRoomId) {
      console.log("没有选中的机房，无法获取趋势数据");
      return;
    }

    const roomName =
      dataCenterOptions.value.find(option => option.value === targetRoomId)
        ?.label || "";
    if (!roomName) {
      console.warn("未找到对应的机房名称:", targetRoomId);
      return;
    }

    // 确定时间范围
    const timeRange = customTimeRange || currentTimeRange.value;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - timeRange.days);

    const params: PerTrendByRoomNameParams = {
      startTime: formatDateToSeconds(startDate),
      endTime: formatDateToSeconds(endDate),
      params: {
        roomName: roomName,
        type: timeType
      }
    };

    console.log("调用机房性能趋势接口，参数:", params);

    const trendRes = await getPerTrendByRoomName(params);
    console.log("机房性能趋势数据:", trendRes);

    // 处理趋势数据
    if (
      trendRes.status === "0" &&
      trendRes.data &&
      Array.isArray(trendRes.data)
    ) {
      const trendData = trendRes.data as PerTrendDataItem[];

      // 转换数据格式以适配组件
      const convertedData = trendData.map(item => ({
        date: item.time, // x轴直接显示time字段，不做任何处理
        show: item.show, // tooltip显示show字段，不做任何处理
        cpu: item.cpuRate, // 百分比直接使用接口返回值，不做任何处理
        memory: item.memRate, // 百分比直接使用接口返回值，不做任何处理
        disk: item.storageRate // 百分比直接使用接口返回值，不做任何处理
      }));

      // 更新对应的数据
      if (timeType === "min") {
        dataCenterMonitorData.value.minuteData = convertedData;
      } else {
        dataCenterMonitorData.value.hourData = convertedData;
      }

      console.log(
        `机房${timeType === "min" ? "分钟" : "小时"}级趋势数据更新成功:`,
        {
          roomName: roomName,
          dataCount: convertedData.length,
          timeRange: getTimeRangeText(),
          sampleData: convertedData.slice(0, 3)
        }
      );

      return convertedData;
    } else {
      console.warn("机房性能趋势接口返回数据格式不正确:", trendRes);
      return null;
    }
  } catch (error) {
    console.error("获取机房性能趋势数据失败:", error);
    return null;
  }
};

// 获取机房详细统计数据
const fetchRoomDetailData = async (
  roomId?: string,
  customTimeRange?: { days: number }
) => {
  // 设置加载状态
  dataStatus.value.dataCenterMonitor.loading = true;
  dataStatus.value.dataCenterMonitor.error = null;

  try {
    // 确定要查询的机房
    const targetRoomId = roomId || selectedDataCenter.value;

    // 如果没有选中机房，清空数据并显示暂无数据状态
    if (!targetRoomId) {
      console.log("没有选中的机房，清空机房监控数据");
      clearRoomMonitorData();
      dataStatus.value.dataCenterMonitor.hasData = false;
      return;
    }

    const roomName =
      dataCenterOptions.value.find(option => option.value === targetRoomId)
        ?.label || "";
    if (!roomName) {
      console.warn("未找到对应的机房名称:", targetRoomId);
      clearRoomMonitorData();
      dataStatus.value.dataCenterMonitor.error = "未找到对应的机房信息";
      return;
    }

    // 确定时间范围
    const timeRange = customTimeRange || currentTimeRange.value;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - timeRange.days);

    const params: ResourceCountByRoomParams = {
      startTime: formatDateToSeconds(startDate),
      endTime: formatDateToSeconds(endDate),
      params: {
        roomName: roomName
      }
    };

    // 资源使用率接口参数
    const usageParams: PerUsageByRoomNameParams = {
      startTime: formatDateToSeconds(startDate),
      endTime: formatDateToSeconds(endDate),
      params: {
        roomName: roomName
      }
    };

    console.log("调用机房统计接口，参数:", params);
    console.log("调用资源使用率接口，参数:", usageParams);

    // 并行调用三个接口：机房统计、资源使用率、性能趋势数据
    const [roomDetailRes, usageRes] = await Promise.all([
      resourceCountByRoom(params),
      getPerUsageByRoomName(usageParams)
    ]);

    // 获取趋势数据（分钟级和小时级）
    await Promise.all([
      fetchRoomTrendData(targetRoomId, timeRange, "min"),
      fetchRoomTrendData(targetRoomId, timeRange, "hour")
    ]);

    console.log("机房详细统计数据:", roomDetailRes);
    console.log("资源使用率数据:", usageRes);

    // 处理机房统计数据
    const roomDetailResponse = roomDetailRes as unknown as ApiResponse;
    if (roomDetailResponse.status === "0" && roomDetailResponse.data) {
      const roomDetailData = roomDetailResponse.data as ResourceCountByRoomData;

      // 更新机房监控数据
      dataCenterMonitorData.value.statistics = {
        cabinets: roomDetailData.cabinetCnt || 0,
        devices: roomDetailData.devCnt || 0,
        alerts: roomDetailData.alarmCnt || 0
      };

      // 更新机房信息 - 从机房选择器选项中获取完整信息
      const selectedRoomOption = dataCenterOptions.value.find(
        option => option.value === targetRoomId
      );

      if (selectedRoomOption) {
        dataCenterMonitorData.value.dataCenterInfo = {
          name: selectedRoomOption.label,
          station: selectedRoomOption.siteName || "未知站点",
          area: selectedRoomOption.zoneName || "未知区域"
        };
      }

      console.log("机房统计数据更新成功:", {
        roomName: roomName,
        statistics: dataCenterMonitorData.value.statistics,
        timeRange: getTimeRangeText()
      });
    } else {
      console.warn("机房统计接口返回数据格式不正确:", roomDetailResponse);
    }

    // 处理资源使用率数据
    const usageResponse = usageRes as PerUsageByRoomNameResponse;
    if (usageResponse.status === "0" && usageResponse.data) {
      // 处理NaN值的辅助函数
      const parseUsageValue = (value: any): number => {
        // 如果是字符串 "NaN" 或者实际的 NaN，返回 0
        if (
          value === "NaN" ||
          isNaN(value) ||
          value === null ||
          value === undefined
        ) {
          return 0;
        }
        // 尝试转换为数字，保留小数点后一位，不做取整
        const numValue = Number(value);
        return isNaN(numValue) ? 0 : Math.round(numValue * 10) / 10;
      };

      // 更新资源使用率数据，处理NaN情况
      dataCenterMonitorData.value.resourceUsage = {
        cpu: parseUsageValue(usageResponse.data.cpuUsed),
        memory: parseUsageValue(usageResponse.data.memUsed),
        disk: parseUsageValue(usageResponse.data.storageUsed)
      };

      console.log("资源使用率数据更新成功:", {
        roomName: roomName,
        originalData: usageResponse.data,
        processedData: dataCenterMonitorData.value.resourceUsage,
        timeRange: getTimeRangeText()
      });
    } else {
      console.warn("资源使用率接口返回数据格式不正确:", usageResponse);
      // 如果资源使用率接口失败，设置默认值
      dataCenterMonitorData.value.resourceUsage = {
        cpu: 0,
        memory: 0,
        disk: 0
      };
    }

    // 只有当至少一个接口成功时才设置hasData为true
    if (roomDetailResponse.status === "0" || usageResponse.status === "0") {
      dataStatus.value.dataCenterMonitor.hasData = true;
    } else {
      dataStatus.value.dataCenterMonitor.error = "机房数据获取失败";
      clearRoomMonitorData();
    }
  } catch (error) {
    console.error("获取机房详细数据失败:", error);
    dataStatus.value.dataCenterMonitor.error = `接口调用失败: ${error.message || error}`;
    clearRoomMonitorData();
    // ElMessage.error("获取机房监控数据失败");
  } finally {
    dataStatus.value.dataCenterMonitor.loading = false;
  }
};

// 调用告警类型分布相关接口的函数
const fetchAlarmTypeData = async () => {
  // 设置加载状态
  dataStatus.value.alarmType.loading = true;
  dataStatus.value.alarmType.error = null;

  try {
    console.log("开始获取告警类型分布数据...");

    // 格式化时间范围参数
    const timeRange = formatTimeRangeForAPI(currentTimeRange.value);
    const params: AlarmGroupByAndTopParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 5,
        fields: "alertType"
      }
    };

    console.log("调用告警类型分布接口，参数:", params);

    // 真实接口调用
    const alarmTypeRes = await getAlarmGroupByAndTop(params);

    console.log("告警类型分布数据:", alarmTypeRes);

    // 处理告警类型分布数据
    if (
      alarmTypeRes.status === "0" &&
      alarmTypeRes.data &&
      Array.isArray(alarmTypeRes.data) &&
      alarmTypeRes.data.length > 0
    ) {
      const data = alarmTypeRes.data;

      // 定义颜色数组
      const colors = ["#FF3838", "#FF7716", "#E4C513", "#0095FF", "#00EAFF"];

      // 转换为组件需要的数据格式
      alarmTypeData.value = data.map((item, index) => ({
        name: item.type || "未知类型",
        value: item.count || 0,
        color: colors[index % colors.length]
      }));

      dataStatus.value.alarmType.hasData = true;
      console.log("告警类型分布数据转换完成:", alarmTypeData.value);
    } else {
      console.warn("告警类型分布接口返回数据为空或格式不正确:", alarmTypeRes);
      // 清空数据，让组件显示"暂无数据"
      alarmTypeData.value = [];
      dataStatus.value.alarmType.hasData = false;
    }

    return alarmTypeRes;
  } catch (error) {
    console.error("获取告警类型分布数据失败:", error);
    dataStatus.value.alarmType.error = `接口调用失败: ${error.message || error}`;
    // ElMessage.error("获取告警类型分布数据失败");
    return null;
  } finally {
    dataStatus.value.alarmType.loading = false;
  }
};

// 调用告警关联应用数量相关接口的函数
const fetchAlarmAppData = async () => {
  // 设置加载状态
  dataStatus.value.alarmApp.loading = true;
  dataStatus.value.alarmApp.error = null;

  try {
    console.log("开始获取告警关联应用数量数据...");

    // 格式化时间范围参数
    const timeRange = formatTimeRangeForAPI(currentTimeRange.value);
    const params: AlarmAppCountParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 0,
        fields: "ciTypeId"
      }
    };

    console.log("调用告警关联应用数量接口，参数:", params);

    // 真实接口调用
    const alarmAppRes = await alarmAppCount(params);

    console.log("告警关联应用数量数据:", alarmAppRes);

    // 处理告警关联应用数量数据
    if (alarmAppRes.status === "0" && typeof alarmAppRes.data === "number") {
      alarmAppData.value.count = alarmAppRes.data;
      // 这里可以根据历史数据计算趋势，暂时设为up
      alarmAppData.value.trend = "up";

      dataStatus.value.alarmApp.hasData = true;
      console.log("告警关联应用数量数据转换完成:", alarmAppData.value);
    } else {
      console.warn("告警关联应用数量接口返回数据格式不正确:", alarmAppRes);
      dataStatus.value.alarmApp.error = "告警关联应用数量数据格式错误";
      // 清空数据
      alarmAppData.value.count = 0;
      alarmAppData.value.trend = "stable";
    }

    return alarmAppRes;
  } catch (error) {
    console.error("获取告警关联应用数量数据失败:", error);
    dataStatus.value.alarmApp.error = `接口调用失败: ${error.message || error}`;
    // 清空数据
    alarmAppData.value.count = 0;
    alarmAppData.value.trend = "stable";
    return null;
  } finally {
    dataStatus.value.alarmApp.loading = false;
  }
};

// 调用告警概览相关接口的函数
const fetchAlarmOverviewData = async () => {
  // 设置加载状态
  dataStatus.value.alarmOverview.loading = true;
  dataStatus.value.alarmOverview.error = null;

  try {
    console.log("开始获取告警概览数据...");

    // 格式化时间范围参数
    const timeRange = formatTimeRangeForAPI(currentTimeRange.value);
    const params: AlarmCountAndDurParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {}
    };

    console.log("调用告警概览接口，参数:", params);

    // 真实接口调用
    const alarmOverviewRes = await getAlarmCountAndDurBySeverity(params);

    console.log("告警概览数据:", alarmOverviewRes);

    // 处理告警概览数据
    if (alarmOverviewRes.status === "0" && alarmOverviewRes.data) {
      const data = alarmOverviewRes.data;

      // 转换为组件需要的数据格式
      alarmOverviewData.value = [
        {
          id: "total-alarms",
          value: data.total_cnt || 0,
          label: "总告警数/故障平均时长",
          unit: `${(data.total_dur || 0).toFixed(1)}min`,
          color: "#00a8ff"
        },
        {
          id: "urgent-alarms",
          value: data["5_cnt"] || 0,
          label: "紧急告警数/故障平均时长",
          unit: `${(data["5_dur"] || 0).toFixed(1)}min`,
          color: "#FF3838"
        },
        {
          id: "important-alarms",
          value: data["4_cnt"] || 0,
          label: "重要告警数/故障平均时长",
          unit: `${(data["4_dur"] || 0).toFixed(1)}min`,
          color: "#FF7716"
        }
      ];

      dataStatus.value.alarmOverview.hasData = true;
      console.log("告警概览数据转换完成:", alarmOverviewData.value);
    } else {
      console.warn("告警概览接口返回数据格式不正确:", alarmOverviewRes);
      dataStatus.value.alarmOverview.error = "告警概览数据格式错误";
      // 保持默认数据
    }

    return alarmOverviewRes;
  } catch (error) {
    console.error("获取告警概览数据失败:", error);
    dataStatus.value.alarmOverview.error = `接口调用失败: ${error.message || error}`;
    // ElMessage.error("获取告警概览数据失败");
    return null;
  } finally {
    dataStatus.value.alarmOverview.loading = false;
  }
};

// 调用资源监控相关接口的函数
const fetchResourceData = async () => {
  // 设置加载状态
  dataStatus.value.resourceOverview.loading = true;
  dataStatus.value.resourceLoad.loading = true;
  dataStatus.value.dataCenterMonitor.loading = true;
  dataStatus.value.resourceOverview.error = null;
  dataStatus.value.resourceLoad.error = null;
  dataStatus.value.dataCenterMonitor.error = null;

  try {
    console.log("开始获取资源监控数据...");

    // 并行调用所有资源监控接口
    const [overviewRes, typeStatsRes, roomListRes] = await Promise.all([
      resourceOverview(),
      resourceCountByType(),
      resourceRoomList()
    ]);

    console.log("资源概览数据:", overviewRes);
    console.log("设备类型统计数据:", typeStatsRes);
    console.log("机房列表数据:", roomListRes);
    let tmpServe = 0;
    let tmpVMServe = 0;
    let tmpCloudServe = 0;

    // 处理设备类型统计数据用于拓扑图
    const typeStatsForTopology = typeStatsRes as unknown as ApiResponse;
    if (
      typeStatsForTopology.status === "0" &&
      typeStatsForTopology.data &&
      Array.isArray(typeStatsForTopology.data)
    ) {
      const typeData = typeStatsForTopology.data as ResourceTypeData[];
      typeData.forEach((item: ResourceTypeData) => {
        if (item.type == "云平台") {
          tmpCloudServe = item.onlineCount;
        }
        if (item.type == "服务器") {
          tmpServe = item.onlineCount;
        }
        if (item.type == "虚拟机") {
          tmpVMServe = item.onlineCount;
        }
      });
    }
    topologyData.value = {
      applicationLayer: [
        { name: "应用01" },
        { name: "应用02" },
        { name: "应用03" },
        { name: "应用04" },
        { name: "应用05" },
        { name: "应用06" }
      ],
      middlewareLayer: [{ name: `平台云${tmpCloudServe}套` }],
      infrastructureLayer: [
        { name: "服务器", count: tmpServe + "台", icon: "服务器-图标@3x.png" },
        { name: "虚拟机", count: tmpVMServe + "台", icon: "虚拟机-图标@3x.png" }
      ]
    };
    console.log("topologyData:", topologyData.value);
    // 处理资源概览数据
    const overviewResponse = overviewRes as unknown as ApiResponse;
    if (overviewResponse.status === "0" && overviewResponse.data) {
      const overviewData = overviewResponse.data as ResourceOverviewData;
      // 更新资源概览统计数据
      resourceOverviewData.value.roomCnt = overviewData.roomCnt || 0;
      resourceOverviewData.value.cabinetCnt = overviewData.cabinetCnt || 0;
      resourceOverviewData.value.devCnt = overviewData.devCnt || 0;
      dataStatus.value.resourceOverview.hasData = true;
    } else {
      console.warn("资源概览接口返回数据格式不正确:", overviewResponse);
      dataStatus.value.resourceOverview.error = "资源概览数据格式错误";
      // 清空数据
      resourceOverviewData.value.roomCnt = 0;
      resourceOverviewData.value.cabinetCnt = 0;
      resourceOverviewData.value.devCnt = 0;
    }

    // 处理设备类型统计数据
    const typeStatsResponse = typeStatsRes as unknown as ApiResponse;
    if (
      typeStatsResponse.status === "0" &&
      typeStatsResponse.data &&
      Array.isArray(typeStatsResponse.data)
    ) {
      const typeData = typeStatsResponse.data as ResourceTypeData[];

      // 验证数据有效性
      const validTypeData = typeData.filter(
        item =>
          item &&
          typeof item === "object" &&
          item.type &&
          item.count !== undefined &&
          item.onlineCount !== undefined
      );

      if (validTypeData.length > 0) {
        // 更新资源负载态势数据
        resourceLoadData.value = validTypeData.map(
          (item: ResourceTypeData, index: number) => {
            const percent =
              item.count > 0
                ? Math.round((item.onlineCount / item.count) * 100)
                : 0;
            return {
              id: `resource-${index}`,
              type: item.type.toLowerCase(),
              value: item.count,
              onlineCount: item.onlineCount,
              percent: percent,
              unit: "",
              label: item.type,
              icon: getResourceIcon(item.type)
            };
          }
        );
        dataStatus.value.resourceLoad.hasData = true;
      } else {
        console.warn("没有有效的设备类型统计数据");
        dataStatus.value.resourceLoad.hasData = false;
      }
    } else {
      console.warn("设备类型统计接口返回数据格式不正确:", typeStatsResponse);
      dataStatus.value.resourceLoad.error = "设备类型统计数据格式错误";
    }

    // 处理机房列表数据
    const roomListResponse = roomListRes as unknown as ApiResponse;
    if (
      roomListResponse.status === "0" &&
      roomListResponse.data &&
      Array.isArray(roomListResponse.data)
    ) {
      const roomData = roomListResponse.data as ResourceRoomData[];

      // 验证数据有效性
      const validRoomData = roomData.filter(
        room => room && typeof room === "object" && room.roomName
      );

      if (validRoomData.length > 0) {
        // 更新机房选择器选项，保存完整的机房信息
        dataCenterOptions.value = validRoomData.map(
          (room: ResourceRoomData) => ({
            value: room.roomID.toString(),
            label: room.roomName,
            siteName: room.siteName,
            zoneName: room.zoneName,
            roomData: room // 保存完整的机房数据
          })
        );

        // 机房列表获取后的处理逻辑
        if (validRoomData.length > 0) {
          // 如果已经有选中的机房，获取其详细统计数据
          if (selectedDataCenter.value) {
            const selectedRoom = validRoomData.find(
              room => room.roomID.toString() === selectedDataCenter.value
            );

            if (selectedRoom) {
              // 更新机房基本信息
              dataCenterMonitorData.value.dataCenterInfo = {
                name: selectedRoom.roomName,
                station: selectedRoom.siteName || "未知站点",
                area: selectedRoom.zoneName || "未知区域"
              };

              // 获取选中机房的详细统计数据
              await fetchRoomDetailData(
                selectedDataCenter.value,
                currentTimeRange.value
              );
            } else {
              // 如果选中的机房不在列表中，清空选择并默认选择第一个
              console.warn("选中的机房不在机房列表中，默认选择第一个机房");
              selectedDataCenter.value = validRoomData[0].roomID.toString();

              // 更新机房基本信息
              dataCenterMonitorData.value.dataCenterInfo = {
                name: validRoomData[0].roomName,
                station: validRoomData[0].siteName || "未知站点",
                area: validRoomData[0].zoneName || "未知区域"
              };

              // 获取第一个机房的详细统计数据
              await fetchRoomDetailData(
                selectedDataCenter.value,
                currentTimeRange.value
              );
            }
          } else {
            // 没有选中机房时，默认选择第一个机房
            console.log("默认选择第一个机房:", validRoomData[0].roomName);
            selectedDataCenter.value = validRoomData[0].roomID.toString();

            // 更新机房基本信息
            dataCenterMonitorData.value.dataCenterInfo = {
              name: validRoomData[0].roomName,
              station: validRoomData[0].siteName || "未知站点",
              area: validRoomData[0].zoneName || "未知区域"
            };

            // 获取第一个机房的详细统计数据
            await fetchRoomDetailData(
              selectedDataCenter.value,
              currentTimeRange.value
            );
          }
        } else {
          // 没有机房数据，清空状态
          clearRoomMonitorData();
        }
      } else {
        console.warn("没有有效的机房列表数据");
        dataStatus.value.dataCenterMonitor.hasData = false;
      }
    } else {
      console.warn("机房列表接口返回数据格式不正确:", roomListResponse);
      dataStatus.value.dataCenterMonitor.error = "机房列表数据格式错误";
    }

    console.log("资源监控数据处理完成:", {
      resourceOverviewData: resourceOverviewData.value,
      resourceLoadData: resourceLoadData.value,
      dataCenterMonitorData: dataCenterMonitorData.value,
      dataStatus: dataStatus.value
    });

    return {
      overview: overviewRes,
      typeStats: typeStatsRes,
      roomList: roomListRes
    };
  } catch (error) {
    console.error("获取资源监控数据失败:", error);

    // 设置错误状态
    dataStatus.value.resourceOverview.error = `接口调用失败: ${error.message || error}`;
    dataStatus.value.resourceLoad.error = `接口调用失败: ${error.message || error}`;
    dataStatus.value.dataCenterMonitor.error = `接口调用失败: ${error.message || error}`;

    // 清空数据
    resourceOverviewData.value.roomCnt = 0;
    resourceOverviewData.value.cabinetCnt = 0;
    resourceOverviewData.value.devCnt = 0;

    // ElMessage.error("获取资源监控数据失败");
    return null;
  } finally {
    dataStatus.value.resourceOverview.loading = false;
    dataStatus.value.resourceLoad.loading = false;
    dataStatus.value.dataCenterMonitor.loading = false;
  }
};
import AlarmOverview from "./components/AlarmOverview.vue";
import AlarmTypePie from "./components/AlarmTypePie.vue";
import AlarmList from "./components/AlarmList.vue";
import SystemTopology from "./components/SystemTopology.vue";

defineOptions({
  name: "ComprehensiveSituationPresentation"
});

// 页面标题
const title = ref("综合态势呈现");
const currentTime = ref("");

// 时间范围状态 - 默认改为"日"，支持自定义精确时间
const currentTimeRange = ref<{
  days: number;
  type: string;
  customTimeRange?: { startTime: string; endTime: string };
}>({
  days: 1,
  type: "day"
});

// 加载状态
const isLoadingTimeData = ref(false);

// 数据状态管理
const dataStatus = ref({
  userOverview: {
    loading: false,
    error: null,
    hasData: false
  },
  docApp: {
    loading: false,
    error: null,
    hasData: false
  },
  hotApps: {
    loading: false,
    error: null,
    hasData: false
  },
  deptTop5: {
    loading: false,
    error: null,
    hasData: false
  },
  documentChart: {
    loading: false,
    error: null,
    hasData: false
  },
  resourceOverview: {
    loading: false,
    error: null,
    hasData: false
  },
  resourceLoad: {
    loading: false,
    error: null,
    hasData: false
  },
  dataCenterMonitor: {
    loading: false,
    error: null,
    hasData: false
  },
  alarmOverview: {
    loading: false,
    error: null,
    hasData: false
  },
  alarmType: {
    loading: false,
    error: null,
    hasData: false
  },
  alarmApp: {
    loading: false,
    error: null,
    hasData: false
  },
  systemTopology: {
    loading: false,
    error: null,
    hasData: false
  }
});

// 资源概览数据 - 完全依赖接口数据
const resourceOverviewData = ref({
  roomCnt: 0, // 机房数量
  cabinetCnt: 0, // 机柜数量
  devCnt: 0 // 设备数量
});

// 机房选择器数据
const selectedDataCenter = ref("");
const dataCenterOptions = ref([]);

// 响应式布局
// 使用CSS实现响应式而不是动态绑定

// Top5热门功能数据 - 完全依赖接口数据
const topFunctionsData = ref({
  columns: [{ label: "排名" }, { label: "功能名称" }, { label: "访问量" }],
  data: []
});

// 部门数据 - 完全依赖接口数据
const deptData = ref({
  columns: [{ label: "排名" }, { label: "部门名称" }, { label: "数量" }],
  data: []
});

// 公文应用态势数据 - 完全依赖接口数据
const documentUsageData = ref({
  rows: [
    {
      id: "document-processing-row",
      icon: "任务工单-图标@3x.png",
      items: [
        {
          id: "document-processing",
          value: 0,
          label: "电子公文处理",
          unit: "次"
        }
      ]
    },
    {
      id: "document-seal-row",
      icon: "任务工单-图标@3x(1).png",
      items: [
        {
          id: "document-seal",
          value: 0,
          label: "电子印章使用",
          unit: "次"
        }
      ]
    },
    {
      id: "document-application-row",
      icon: "任务工单-图标@3x(2).png",
      items: [
        {
          id: "document-application",
          value: 0,
          label: "电子公文应用",
          unit: "次"
        }
      ]
    }
  ]
});

// 公文应用图表数据 - 完全依赖接口数据，但提供最小默认数据确保图表显示
const documentChartData = ref([
  {
    date: "暂无",
    documentProcessing: 0,
    documentSeal: 0,
    documentApplication: 0
  }
]);

// 系统运行态势 - 告警概览数据 - 完全依赖接口数据
const alarmOverviewData = ref([
  {
    id: "total-alarms",
    value: 0,
    label: "总告警数/故障平均时长",
    unit: "0.0min",
    color: "#00a8ff"
  },
  {
    id: "urgent-alarms",
    value: 0,
    label: "紧急告警数/故障平均时长",
    unit: "0.0min",
    color: "#FF3838"
  },
  {
    id: "important-alarms",
    value: 0,
    label: "重要告警数/故障平均时长",
    unit: "0.0min",
    color: "#FF7716"
  }
]);

// 系统运行态势 - 告警类型分布数据 - 完全依赖接口数据，无数据时显示"暂无数据"
const alarmTypeData = ref([]);

// 告警关联应用数量数据 - 完全依赖接口数据
const alarmAppData = ref({
  count: 0,
  trend: "up" // up, down, stable
});

// 告警列表数据现在由 AlarmList 组件内部通过真实接口获取

// 系统运行态势 - 告警综合关联统计数据 - 完全依赖接口数据
const alarmStatisticsData = ref([
  {
    id: "cert-alarms",
    label: "认证场景",
    value: 0
  },
  {
    id: "network-alarms",
    label: "通讯场景",
    value: 0
  },
  {
    id: "video-conf-alarms",
    label: "视频会议",
    value: 0
  },
  {
    id: "video-monitor-alarms",
    label: "视频监控",
    value: 0
  }
]);

// 导入资源图标
import cpuIcon from "@/views/comprehensiveSituationPresentation/assets/CPU数量-图标@3x.png";
import memoryIcon from "@/views/comprehensiveSituationPresentation/assets/内存容量-图标@3x.png";
import diskIcon from "@/views/comprehensiveSituationPresentation/assets/磁盘容量-图标@3x.png";
import alarmAppIcon from "@/views/comprehensiveSituationPresentation/assets/告警关联应用大-图标@2x.png";
// 导入资源概览卡片图标
import roomIcon from "@/views/comprehensiveSituationPresentation/assets/机房-图标@2x.png";
import cabinetIcon from "@/views/comprehensiveSituationPresentation/assets/机柜-图标@2x.png";
import appServiceIcon from "@/views/comprehensiveSituationPresentation/assets/应用服务-图标@2x.png";

// 资源利用率趋势图数据
const resourceUsageChartData = ref([
  {
    id: "cpu-usage-chart",
    // title: '// CPU利用率趋势图',
    color: "#0095FF", // 蓝色
    data: [
      { date: "09-11", value: 200 },
      { date: "09-12", value: 300 },
      { date: "09-13", value: 200 },
      { date: "09-14", value: 150 },
      { date: "09-15", value: 200 },
      { date: "09-16", value: 120 },
      { date: "09-17", value: 180 },
      { date: "09-18", value: 250 },
      { date: "09-19", value: 220 },
      { date: "09-20", value: 250 },
      { date: "09-21", value: 330 },
      { date: "09-22", value: 300 }
    ]
  },
  {
    id: "memory-usage-chart",
    // title: '// 内存利用率趋势图',
    color: "#0095FF", // 蓝色
    data: [
      { date: "09-11", value: 350 },
      { date: "09-12", value: 220 },
      { date: "09-13", value: 320 },
      { date: "09-14", value: 250 },
      { date: "09-15", value: 300 },
      { date: "09-16", value: 280 },
      { date: "09-17", value: 200 },
      { date: "09-18", value: 120 },
      { date: "09-19", value: 150 },
      { date: "09-20", value: 200 },
      { date: "09-21", value: 300 },
      { date: "09-22", value: 180 }
    ]
  },
  {
    id: "disk-usage-chart",
    // title: '// 磁盘利用率趋势图',
    color: "#0095FF", // 蓝色
    data: [
      { date: "09-11", value: 120 },
      { date: "09-12", value: 180 },
      { date: "09-13", value: 120 },
      { date: "09-14", value: 280 },
      { date: "09-15", value: 250 },
      { date: "09-16", value: 180 },
      { date: "09-17", value: 220 },
      { date: "09-18", value: 250 },
      { date: "09-19", value: 200 },
      { date: "09-20", value: 220 },
      { date: "09-21", value: 300 },
      { date: "09-22", value: 150 }
    ]
  }
]);

// 资源负载态势数据 - 完全依赖接口数据
const resourceLoadData = ref([]);

// 加载资源利用率趋势图数据
const loadResourceUsageChartData = async () => {
  try {
    // 这里可以调用实际的API接口
    // const response = await fetch('/api/resource-usage-chart-data');
    // const data = await response.json();
    // resourceUsageChartData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟数据更新
    // 已经在初始化时设置了数据，这里可以进行更新
  } catch (error) {
    console.error("加载资源利用率趋势图数据失败:", error);
    // ElMessage.error("加载资源利用率趋势图数据失败");
  }
};

// 加载资源负载态势数据 - 现在由 fetchResourceData 函数处理
const loadResourceLoadData = async () => {
  try {
    // 资源负载数据现在由 fetchResourceData 函数通过真实接口获取
    // 这个函数保留用于兼容性，实际数据加载在 fetchResourceData 中完成
    console.log("资源负载数据由 fetchResourceData 函数处理");
  } catch (error) {
    console.error("加载资源负载态势数据失败:", error);
    // ElMessage.error("加载资源负载态势数据失败");
  }
};

// 加载用户使用态势数据
const loadUserUsageData = async () => {
  try {
    // 这里可以调用实际的API接口
    // const response = await fetch('/api/user-usage-data');
    // const data = await response.json();
    // userUsageData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 可以动态添加或修改数据行
    // 例如：添加一个新的数据行
    // userUsageData.value.rows.push({
    //     id: 'new-row',
    //     icon: 'some-icon.png',
    //     items: [
    //         {
    //             id: 'new-item-1',
    //             value: 100,
    //             label: '新数据项',
    //             unit: '个'
    //         }
    //     ]
    // });

    // // ElMessage.success('用户使用态势数据加载成功');
  } catch (error) {
    console.error("加载用户使用态势数据失败:", error);
    // ElMessage.error("加载用户使用态势数据失败");
  }
};

// 加载机房监控数据
const loadDataCenterMonitorData = async () => {
  try {
    dataCenterLoading.value = true;
    dataCenterError.value = "";

    console.log("开始加载机房监控数据...");

    // 这里可以调用实际的API接口
    // const response = await fetch('/api/datacenter-monitor-data');
    // const data = await response.json();
    // dataCenterMonitorData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 确保数据已经设置
    console.log("机房监控数据:", dataCenterMonitorData.value);

    // 模拟实时数据更新
    // 可以定期更新资源使用率数据
    // setInterval(() => {
    //   dataCenterMonitorData.value.resourceUsage.cpu = Math.floor(Math.random() * 100);
    //   dataCenterMonitorData.value.resourceUsage.memory = Math.floor(Math.random() * 100);
    //   dataCenterMonitorData.value.resourceUsage.disk = Math.floor(Math.random() * 100);
    // }, 5000);

    console.log("机房监控数据加载成功");
  } catch (error) {
    console.error("加载机房监控数据失败:", error);
    dataCenterError.value = "加载机房监控数据失败";
    // ElMessage.error("加载机房监控数据失败");
  } finally {
    dataCenterLoading.value = false;
  }
};

// 处理时间范围变化
const handleTimeRangeChange = async (range: string) => {
  console.log("时间范围切换到:", range);
  dataCenterMonitorData.value.selectedTimeRange = range;

  // 根据时间范围类型获取对应的趋势数据
  if (selectedDataCenter.value) {
    const timeType = range === "分钟" ? "min" : "hour";
    await fetchRoomTrendData(
      selectedDataCenter.value,
      currentTimeRange.value,
      timeType
    );
  }
};

// 处理图表类型变化
const handleChartTypeChange = (type: string) => {
  console.log("图表类型切换到:", type);
  // 这里可以根据需要处理图表类型变化
};

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

// 处理顶部时间范围切换 - 使用优雅的接口管理器，支持自定义精确时间
const handleTopTimeRangeChange = async (days: number, type: string, customTimeRange?: { startTime: string; endTime: string }) => {
  console.log("🔄 时间范围切换:", { days, type, customTimeRange });

  // 更新当前时间范围状态
  currentTimeRange.value = { days, type, customTimeRange };

  // 设置加载状态
  isLoadingTimeData.value = true;

  const startTime = Date.now();

  try {
    // 创建时间范围切换专用的接口管理器
    const createTimeRangeApiManager = () => {
      const timeRangeApiGroups: Record<
        string,
        { name: string; apis: ApiInfo[] }
      > = {
        // 时间敏感的核心接口
        timeSensitive: {
          name: "时间敏感数据",
          apis: [
            {
              name: "公文应用态势数据",
              fn: () => fetchDocAppData(days),
              critical: true
            },
            {
              name: "热门应用数据",
              fn: () => fetchUserOverviewHotApp(days),
              critical: false
            },
            {
              name: "告警概览数据",
              fn: () => fetchAlarmOverviewData(),
              critical: false
            },
            {
              name: "告警类型分布数据",
              fn: () => fetchAlarmTypeData(),
              critical: false
            },
            {
              name: "告警关联应用数量数据",
              fn: () => fetchAlarmAppData(),
              critical: false
            }
          ]
        },
        // 机房相关接口（如果有选中机房）
        ...(selectedDataCenter.value
          ? {
              roomData: {
                name: "机房监控数据",
                apis: [
                  {
                    name: "机房详细统计",
                    fn: () =>
                      fetchRoomDetailData(selectedDataCenter.value, { days }),
                    critical: false
                  }
                ]
              }
            }
          : {})
      };

      const callTimeRangeApiGroup = async (groupKey: string) => {
        const group = timeRangeApiGroups[groupKey];
        if (!group)
          return {
            groupName: groupKey,
            total: 0,
            success: 0,
            failed: 0,
            criticalFailures: 0,
            results: []
          };

        console.log(`🔄 重新加载${group.name}...`);

        const results = await Promise.allSettled(
          group.apis.map((api: ApiInfo) => callWithRetry(api))
        );

        const successCount = results.filter(
          (r: PromiseSettledResult<ApiResult>) =>
            r.status === "fulfilled" && r.value.success
        ).length;

        const failedResults = results
          .filter(
            (r: PromiseSettledResult<ApiResult>) =>
              r.status === "fulfilled" && !r.value.success
          )
          .map((r: PromiseFulfilledResult<ApiResult>) => r.value);

        console.log(
          `✅ ${group.name}重新加载完成: ${successCount}/${group.apis.length} 成功`
        );

        // 处理失败的接口
        const criticalFailures = failedResults.filter(
          (r: ApiResult) => r.critical
        );
        if (criticalFailures.length > 0) {
          console.error(
            `❌ 关键接口重新加载失败:`,
            criticalFailures.map((r: ApiResult) => r.apiName)
          );
        }

        return {
          groupName: group.name,
          total: group.apis.length,
          success: successCount,
          failed: failedResults.length,
          criticalFailures: criticalFailures.length,
          results: results
        };
      };

      return { callTimeRangeApiGroup, timeRangeApiGroups };
    };

    const { callTimeRangeApiGroup } = createTimeRangeApiManager();

    // 复用主接口管理器的重试函数
    const { callApiGroup } = createApiManager();

    // 内部重试函数（简化版）
    const callWithRetry = async (
      apiInfo: ApiInfo,
      retries = 0
    ): Promise<ApiResult> => {
      try {
        const result = await apiInfo.fn();
        return { success: true, data: result, apiName: apiInfo.name };
      } catch (error) {
        if (retries < 2) {
          const delay = 1000 * Math.pow(2, retries);
          await new Promise(resolve => setTimeout(resolve, delay));
          return callWithRetry(apiInfo, retries + 1);
        }
        return {
          success: false,
          error,
          apiName: apiInfo.name,
          critical: apiInfo.critical
        };
      }
    };

    // 按优先级重新加载数据
    const timeSensitiveResults = await callTimeRangeApiGroup("timeSensitive");
    const roomDataResults = selectedDataCenter.value
      ? await callTimeRangeApiGroup("roomData")
      : null;

    // 汇总结果
    const allResults = [timeSensitiveResults, roomDataResults].filter(Boolean);
    const totalSuccess = allResults.reduce(
      (sum, r) => sum + (r?.success || 0),
      0
    );
    const totalApis = allResults.reduce((sum, r) => sum + (r?.total || 0), 0);
    const totalCriticalFailures = allResults.reduce(
      (sum, r) => sum + (r?.criticalFailures || 0),
      0
    );

    const loadTime = Date.now() - startTime;
    const timeRangeText = getTimeRangeText();

    console.log(`🎉 时间范围切换完成! 耗时: ${loadTime}ms`);
    console.log(`📊 数据重新加载: ${totalSuccess}/${totalApis} 成功`);
    console.log(`⏰ 当前时间范围: ${timeRangeText}`);

    // 显示切换结果
    if (totalCriticalFailures === 0) {
      // ElMessage.success(`时间范围已切换到${timeRangeText} (${loadTime}ms)`);
    } else {
      // ElMessage.warning(`时间范围已切换到${timeRangeText}，但${totalCriticalFailures}个关键接口失败`);
    }
  } catch (error) {
    console.error("💥 时间范围切换异常:", error);
    // ElMessage.error(`时间范围切换失败: ${error.message || error}`);
  } finally {
    isLoadingTimeData.value = false;
  }
};

// 单独调用热门应用接口的函数 - 使用新的数据管理器
const fetchUserOverviewHotApp = async (days: number) => {
  console.log("🔄 重新获取热门应用数据（使用新管理器），天数:", days);

  try {
    // 使用新的数据获取器重新获取热门应用数据
    await userSituationDataFetcher.refetchHotApps(days);

    // 更新旧的dataStatus以保持兼容性
    const state = userSituationManager.getState();
    dataStatus.value.hotApps.loading = state.loadingState.hotApps;
    dataStatus.value.hotApps.hasData = state.processedData.hotApps.length > 0;
    dataStatus.value.hotApps.error = state.errors.hotApps;

    console.log("✅ 时间范围切换：热门应用数据更新完成:", {
      dataState: state.dataState,
      hotAppsCount: state.processedData.hotApps.length,
      apps: state.processedData.hotApps
    });

    return { success: true, data: state.processedData.hotApps };
  } catch (error) {
    console.error("❌ 重新获取热门应用数据失败:", error);
    return { success: false, error: error.message || error };
  }
};

// 获取时间范围文本显示
const getTimeRangeText = () => {
  const { days, type } = currentTimeRange.value;
  switch (type) {
    case "day":
      return "今日";
    case "week":
      return "近一周";
    case "month":
      return "近一月";
    case "custom":
      return `自定义 ${days} 天`;
    default:
      return `${days} 天`;
  }
};

// 通用时间格式化函数 - 精确到秒级
const formatDateToSeconds = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化时间范围参数用于接口调用 - 精确到秒级，支持自定义时间
const formatTimeRangeForAPI = (timeRange: {
  days: number;
  customTimeRange?: { startTime: string; endTime: string }
}) => {
  // 如果有自定义时间范围，直接使用
  if (timeRange.customTimeRange) {
    return {
      startTime: timeRange.customTimeRange.startTime,
      endTime: timeRange.customTimeRange.endTime
    };
  }

  // 否则使用默认的天数计算
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - timeRange.days);

  return {
    startTime: formatDateToSeconds(startDate),
    endTime: formatDateToSeconds(endDate)
  };
};

// 监听机房选择器变化 - 同时更新机房监控和系统架构
watch(selectedDataCenter, async (newRoomId: string, oldRoomId: string) => {
  console.log("机房选择变化:", { from: oldRoomId, to: newRoomId });

  // 处理机房清空的情况
  if (!newRoomId) {
    console.log("机房选择器被清空，清空机房监控数据和系统架构数据");
    clearRoomMonitorData();
    // 系统架构组件会通过props自动处理空机房的情况
    return;
  }

  // 处理机房选择的情况
  if (newRoomId !== oldRoomId) {
    console.log("选择了新机房，获取机房详细数据和系统架构数据");

    // 并行获取机房监控数据和系统架构数据
    const roomName =
      dataCenterOptions.value.find(option => option.value === newRoomId)
        ?.label || newRoomId;

    try {
      // 使用当前选中的时间范围获取新机房的统计数据
      await fetchRoomDetailData(newRoomId, currentTimeRange.value);

      console.log(`已切换到机房: ${roomName}，时间范围: ${getTimeRangeText()}`);
      console.log("机房监控数据和系统架构数据将自动更新");

      // 系统架构组件会通过watch监听selectedDataCenter的变化自动更新
      // 不需要在这里手动调用，避免重复请求

    } catch (error) {
      console.error("切换机房时获取数据失败:", error);
    }
  }
});

// 处理热门功能数据
const processFunctionData = (functionList: any[]) => {
  if (!functionList || functionList.length === 0) return;

  // 对功能按访问量排序
  const sortedFunctions = [...functionList].sort((a, b) => b.count - a.count);

  // 取前5个功能
  const top5Functions = sortedFunctions.slice(0, 5);

  // 找出最大访问量作为100%基准
  const maxCount = top5Functions.length > 0 ? top5Functions[0].count : 0;

  // 转换为TableTop5组件需要的数据格式
  topFunctionsData.value.data = top5Functions.map(func => ({
    label: func.name,
    value: func.count,
    percent: maxCount > 0 ? Math.round((func.count / maxCount) * 100) : 0
  }));
};

// 热门功能数据 - 完全依赖接口数据
const mockFunctionData = [];

// 机房监控数据 - 预留给后续接口对接
const dataCenterMonitorData = ref({
  dataCenterInfo: {
    name: "请选择机房",
    station: "暂无数据",
    area: "暂无数据"
  },
  resourceUsage: {
    cpu: 0,
    memory: 0,
    disk: 0
  },
  statistics: {
    cabinets: 0,
    devices: 0,
    alerts: 0
  },
  // 分钟级数据
  minuteData: [
    { date: "14:26", cpu: 8200, memory: 7800, disk: 6200 },
    { date: "14:27", cpu: 7900, memory: 7600, disk: 6100 },
    { date: "14:28", cpu: 8100, memory: 7700, disk: 6000 },
    { date: "14:29", cpu: 8300, memory: 7900, disk: 6300 },
    { date: "14:30", cpu: 8000, memory: 7500, disk: 6000 },
    { date: "14:31", cpu: 7800, memory: 7400, disk: 5900 },
    { date: "14:32", cpu: 8200, memory: 7800, disk: 6200 },
    { date: "14:33", cpu: 8400, memory: 8000, disk: 6400 },
    { date: "14:34", cpu: 8100, memory: 7700, disk: 6100 },
    { date: "14:35", cpu: 7900, memory: 7500, disk: 5800 }
  ],
  // 小时级数据
  hourData: [
    { date: "10:00", cpu: 7500, memory: 7200, disk: 5800 },
    { date: "11:00", cpu: 8000, memory: 7500, disk: 6000 },
    { date: "12:00", cpu: 8200, memory: 7800, disk: 6200 },
    { date: "13:00", cpu: 7800, memory: 7400, disk: 5900 },
    { date: "14:00", cpu: 8100, memory: 7700, disk: 6100 },
    { date: "15:00", cpu: 7900, memory: 7600, disk: 6000 },
    { date: "16:00", cpu: 8300, memory: 7900, disk: 6300 },
    { date: "17:00", cpu: 8000, memory: 7500, disk: 6000 }
  ],
  selectedTimeRange: "分钟"
});

// 机房监控状态
const dataCenterLoading = ref(false);
const dataCenterError = ref("");

// 接口管理器 - 高级优雅的接口调用管理
const createApiManager = () => {
  // 接口分组配置
  const apiGroups: Record<string, { name: string; apis: ApiInfo[] }> = {
    // 核心业务接口组 - 优先级最高
    core: {
      name: "核心业务数据",
      apis: [
        {
          name: "用户态势数据",
          fn: () => fetchUserOverviewData(),
          critical: true
        },
        { name: "公文态势数据", fn: () => fetchDocAppData(currentTimeRange.value.days), critical: true },
        { name: "资源监控数据", fn: () => fetchResourceData(), critical: true }
      ]
    },
    // 告警相关接口组
    alarm: {
      name: "告警监控数据",
      apis: [
        {
          name: "告警概览数据",
          fn: () => fetchAlarmOverviewData(),
          critical: false
        },
        {
          name: "告警类型分布数据",
          fn: () => fetchAlarmTypeData(),
          critical: false
        },
        {
          name: "告警关联应用数量数据",
          fn: () => fetchAlarmAppData(),
          critical: false
        }
      ]
    },
    // 辅助功能接口组 - 优先级较低
    auxiliary: {
      name: "辅助功能数据",
      apis: [
        // 移除重复的用户使用数据调用，避免数据竞争
        // { name: "用户使用数据", fn: () => loadUserUsageData(), critical: false },
        {
          name: "资源使用图表数据",
          fn: () => loadResourceUsageChartData(),
          critical: false
        },
        {
          name: "资源负载数据",
          fn: () => loadResourceLoadData(),
          critical: false
        },
        {
          name: "机房监控数据",
          fn: () => loadDataCenterMonitorData(),
          critical: false
        }
      ]
    }
  };

  // 重试配置
  const retryConfig = {
    maxRetries: 2,
    retryDelay: 1000,
    backoffMultiplier: 2
  };

  // 带重试的接口调用函数
  const callWithRetry = async (
    apiInfo: ApiInfo,
    retries = 0
  ): Promise<ApiResult> => {
    try {
      const result = await apiInfo.fn();
      return { success: true, data: result, apiName: apiInfo.name };
    } catch (error) {
      console.warn(`${apiInfo.name}调用失败 (第${retries + 1}次):`, error);

      if (retries < retryConfig.maxRetries) {
        const delay =
          retryConfig.retryDelay *
          Math.pow(retryConfig.backoffMultiplier, retries);
        console.log(`${apiInfo.name}将在${delay}ms后重试...`);

        await new Promise(resolve => setTimeout(resolve, delay));
        return callWithRetry(apiInfo, retries + 1);
      }

      return {
        success: false,
        error,
        apiName: apiInfo.name,
        critical: apiInfo.critical
      };
    }
  };

  // 分组并行调用接口
  const callApiGroup = async (groupKey: string) => {
    const group = apiGroups[groupKey];
    console.log(`开始加载${group.name}...`);

    const results = await Promise.allSettled(
      group.apis.map((api: ApiInfo) => callWithRetry(api))
    );

    const successCount = results.filter(
      (r: PromiseSettledResult<ApiResult>) =>
        r.status === "fulfilled" && r.value.success
    ).length;

    const failedResults = results
      .filter(
        (r: PromiseSettledResult<ApiResult>) =>
          r.status === "fulfilled" && !r.value.success
      )
      .map((r: PromiseFulfilledResult<ApiResult>) => r.value);

    console.log(
      `${group.name}加载完成: ${successCount}/${group.apis.length} 成功`
    );

    // 处理失败的关键接口
    const criticalFailures = failedResults.filter((r: ApiResult) => r.critical);
    if (criticalFailures.length > 0) {
      console.error(
        `关键接口失败:`,
        criticalFailures.map((r: ApiResult) => r.apiName)
      );
      // ElMessage.warning(`关键数据加载失败: ${criticalFailures.map((r: ApiResult) => r.apiName).join(', ')}`);
    }

    // 处理失败的非关键接口
    const nonCriticalFailures = failedResults.filter(
      (r: ApiResult) => !r.critical
    );
    if (nonCriticalFailures.length > 0) {
      console.warn(
        `非关键接口失败:`,
        nonCriticalFailures.map((r: ApiResult) => r.apiName)
      );
    }

    return {
      groupName: group.name,
      total: group.apis.length,
      success: successCount,
      failed: failedResults.length,
      criticalFailures: criticalFailures.length,
      results: results
    };
  };

  return { callApiGroup, apiGroups };
};

// 页面初始化
onMounted(async () => {
  updateTime();
  setInterval(updateTime, 1000);

  console.log("🚀 开始优雅地加载页面数据...");
  const startTime = Date.now();

  try {
    const { callApiGroup } = createApiManager();

    // 按优先级顺序加载接口组
    console.log("📊 第一阶段: 加载核心业务数据...");
    const coreResults = await callApiGroup("core");

    console.log("🚨 第二阶段: 加载告警监控数据...");
    const alarmResults = await callApiGroup("alarm");

    console.log("🔧 第三阶段: 加载辅助功能数据...");
    const auxiliaryResults = await callApiGroup("auxiliary");

    // 汇总加载结果
    const allResults = [coreResults, alarmResults, auxiliaryResults];
    const totalSuccess = allResults.reduce((sum, r) => sum + r.success, 0);
    const totalApis = allResults.reduce((sum, r) => sum + r.total, 0);
    const totalCriticalFailures = allResults.reduce(
      (sum, r) => sum + r.criticalFailures,
      0
    );

    const loadTime = Date.now() - startTime;

    console.log(`✅ 页面数据加载完成! 耗时: ${loadTime}ms`);
    console.log(
      `📈 成功率: ${totalSuccess}/${totalApis} (${Math.round((totalSuccess / totalApis) * 100)}%)`
    );

    if (totalCriticalFailures === 0) {
      console.log("🎉 所有关键接口加载成功!");
    } else {
      console.warn(`⚠️  ${totalCriticalFailures} 个关键接口加载失败`);
    }

    // 输出当前状态摘要
    console.log("📋 当前页面状态:", {
      timeRange: getTimeRangeText(),
      selectedRoom: selectedDataCenter.value
        ? dataCenterOptions.value.find(
            opt => opt.value === selectedDataCenter.value
          )?.label
        : "未选择",
      dataStatus: {
        resourceOverview: dataStatus.value.resourceOverview.hasData,
        resourceLoad: dataStatus.value.resourceLoad.hasData,
        dataCenterMonitor: dataStatus.value.dataCenterMonitor.hasData,
        userOverview: dataStatus.value.userOverview.hasData,
        alarmOverview: dataStatus.value.alarmOverview.hasData
      },
      loadTime: `${loadTime}ms`,
      successRate: `${Math.round((totalSuccess / totalApis) * 100)}%`
    });

    // 处理热门功能数据
    processFunctionData(mockFunctionData);

    // 显示加载完成消息
    if (totalCriticalFailures === 0) {
      // ElMessage.success(`页面数据加载完成 (${loadTime}ms)`);
    } else {
      // ElMessage.warning(`页面数据部分加载完成，${totalCriticalFailures}个关键接口失败`);
    }
  } catch (error) {
    console.error("💥 页面数据加载异常:", error);
    // ElMessage.error(`页面数据加载失败: ${error.message || error}`);
  }
});
</script>

<template>
  <background-box class="comprehensive-situation h-full w-full">
    <!-- 资源概览 -->
    <title-header
      :title="title"
      @time-range-change="handleTopTimeRangeChange"
    />

    <!-- 主体内容区 -->
    <div class="main-content flex h-[calc(100%-80px)]">
      <!-- 左侧系统资源监控区 -->
      <div class="left-panel p-2 w-1/4 ml-2">
        <!-- 资源概览 -->
        <h4>资源概览</h4>
        <div class="resource-overview-container">
          <div class="resource-overview-grid">
            <!-- 顶部卡片区域 -->
            <div class="resource-cards-grid">
              <div class="resource-card">
                <div class="card-content">
                  <div class="icon-container icon-blue">
                    <img
                      :src="roomIcon"
                      alt="机房图标"
                      class="card-icon-img"
                    />
                  </div>
                  <div class="card-text">
                    <div class="card-value">
                      {{ resourceOverviewData.roomCnt }}
                    </div>
                    <div class="card-label">机房</div>
                  </div>
                </div>
              </div>

              <div class="resource-card">
                <div class="card-content">
                  <div class="icon-container icon-green">
                    <img
                      :src="cabinetIcon"
                      alt="机柜图标"
                      class="card-icon-img"
                    />
                  </div>
                  <div class="card-text">
                    <div class="card-value">
                      {{ resourceOverviewData.cabinetCnt }}
                    </div>
                    <div class="card-label">机柜</div>
                  </div>
                </div>
              </div>

              <div class="resource-card">
                <div class="card-content">
                  <div class="icon-container icon-cyan">
                    <img
                      :src="appServiceIcon"
                      alt="应用服务图标"
                      class="card-icon-img"
                    />
                  </div>
                  <div class="card-text">
                    <div class="card-value">
                      {{ resourceOverviewData.devCnt.toLocaleString() }}
                    </div>
                    <div class="card-label">设备</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 中间监控区域 -->
            <div class="resource-load-container">
              <resource-load-situation :items="resourceLoadData" />
            </div>
          </div>
          <div
            style="
              padding: 10px;
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <h4>资源负载</h4>
            <div style="display: flex; align-items: center">
              机房:
              <el-select
                v-model="selectedDataCenter"
                clearable
                placeholder="请选择机房"
                style="width: 133px"
                class="ml-2"
                :loading="dataStatus.dataCenterMonitor.loading"
                :disabled="
                  dataStatus.dataCenterMonitor.loading ||
                  dataCenterOptions.length === 0
                "
              >
                <el-option
                  v-for="item in dataCenterOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div
            style="
              background: #fff;
              height: 56%;
              width: 100%;
              box-sizing: border-box;
            "
          >
            <data-center-monitor
              :data-center-info="dataCenterMonitorData.dataCenterInfo"
              :resource-usage="dataCenterMonitorData.resourceUsage"
              :statistics="dataCenterMonitorData.statistics"
              :minute-data="dataCenterMonitorData.minuteData"
              :hour-data="dataCenterMonitorData.hourData"
              :selected-time-range="dataCenterMonitorData.selectedTimeRange"
              :loading="
                dataCenterLoading || dataStatus.dataCenterMonitor.loading
              "
              :error="dataCenterError || dataStatus.dataCenterMonitor.error"
              :has-data="dataStatus.dataCenterMonitor.hasData"
              @time-range-change="handleTimeRangeChange"
              @chart-type-change="handleChartTypeChange"
            />
          </div>
        </div>

        <!-- <border-box :img="'resourceOverview'">
          <resource-cards />
        </border-box> -->

        <!-- 部门公文处理排名 -->
        <!-- <border-box :img="'departmentRanking'">
          <table-top5 :columns="deptData.columns" :data="deptData.data" />
        </border-box> -->

        <!-- 资源负载态势 -->

        <!-- <resource-usage-chart
              :charts="resourceUsageChartData"
              height="133px"
              backgroundColor="#ffffff"
            /> -->
      </div>

      <!-- 中间面板 - 预留给新版本 -->
      <div class="center-panel p-2 flex-1 pt-0">
        <!-- 系统架构拓扑图 -->
        <SystemTopology
          :selected-room="selectedDataCenter"
          :room-options="dataCenterOptions"
          :time-range="currentTimeRange"
          :loading="dataStatus.systemTopology.loading"
          :error="dataStatus.systemTopology.error"
          @update:loading="(loading) => dataStatus.systemTopology.loading = loading"
          @update:error="(error) => dataStatus.systemTopology.error = error"
        />
        <!-- 系统运行态势 -->
        <border-box>
          <div style="padding: 10px">系统运行态势</div>
          <div class="system-run-container flex">
            <!-- 左侧告警概览 -->
            <div class="alarm-overview-container w-1/3">
              <alarm-overview
                :items="alarmOverviewData"
                v-loading="dataStatus.alarmOverview.loading"
                element-loading-text="正在加载告警数据..."
              />
            </div>

            <!-- 中间告警类型分布饼图 -->
            <div class="alarm-type-container w-1/3">
              <alarm-type-pie
                :data="alarmTypeData"
                v-loading="dataStatus.alarmType.loading"
                element-loading-text="正在加载告警类型数据..."
              />
            </div>

            <!-- 右侧告警综合关联统计 -->
            <div class="alarm-statistics-container w-1/3">
              <!-- <alarm-statistics :items="alarmStatisticsData" /> -->
              <div
                style="
                  padding: 10px;
                  background-color: rgba(255, 255, 255, 0.9);
                  border-radius: 4px;
                  box-sizing: border-box;
                  border: 1px solid #e4e7ed;
                "
                v-loading="dataStatus.alarmApp.loading"
                element-loading-text="正在加载告警关联应用数据..."
              >
                <div>
                  <div class="hot-apps-title">
                    <div class="hot-apps-icon">
                      <div></div>
                      <div style="margin-left: 3px"></div>
                    </div>
                    <span class="hot-apps-text">告警关联应用统计</span>
                  </div>
                  <div class="alarm-app-content">
                    <div class="alarm-app-icon">
                      <img
                        :src="alarmAppIcon"
                        alt="告警关联应用图标"
                        class="alarm-app-icon-img"
                      />
                    </div>
                    <div class="alarm-app-stats">
                      <div class="alarm-app-number">
                        {{ alarmAppData.count }} <span style="font-size: 13px">个</span>
                      </div>
                      <div class="alarm-app-label">告警关联应用数量</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 告警列表 -->
          <div class="alarm-list-container w-full mt-2">
            <alarm-list
              :time-range="currentTimeRange"
              :default-page-size="5"
              :default-current-page="1"
            />
          </div>
        </border-box>
      </div>

      <!-- 右侧用户及告警统计区 -->
      <div class="right-panel p-2 w-1/4 pt-0">
        <!-- 用户使用态势 -->
        <border-box>
          <div
            class="user-data-container"
            v-loading="isLoadingTimeData"
            element-loading-text="正在切换时间范围..."
          >
            <user-usage-situation
              :key="`user-usage-${userSituationState.version}-${userSituationState.lastUpdate}`"
              :rows="userUsageData.rows"
              :hot-apps="hotAppsData"
            />
          </div>
        </border-box>

        <!-- 公文应用态势 -->
        <border-box>
          <div
            class="document-data-container"
            v-loading="isLoadingTimeData"
            element-loading-text="正在切换时间范围..."
          >
            <!-- 时间范围指示器 -->
            <div class="time-range-indicator">
              <span class="indicator-text">
                当前数据范围: {{ getTimeRangeText() }}
                <span v-if="isLoadingTimeData" class="loading-text"
                  >(加载中...)</span
                >
              </span>
            </div>

            <user-usage-situation
              :title="'公文应用态势'"
              :miniTitle="'调用部门-top5'"
              :isShowMiniTitle="false"
              :rows="documentUsageData.rows"
              :hot-apps="deptTop5Data"
              :documentChartData="documentChartData"
              class="document-data-container-user-usage-situation"
            />
            <!-- <div class="document-chart-container">
          <table-top5 :columns="deptData.columns" :data="deptData.data" />
            </div> -->
          </div>
        </border-box>

        <!-- 部门公文处理TOP5 -->
        <!-- <border-box>
          <table-top5 :columns="deptData.columns" :data="deptData.data" />
        </border-box> -->
      </div>
    </div>
  </background-box>
</template>

<style lang="scss" scoped>
.comprehensive-situation {
  width: 100%;
  height: 100%;
  overflow: auto;

  .left-panel {
    background: #fff;
    box-sizing: border-box;
    padding: 10px;
  }
  .hot-apps-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .hot-apps-icon {
      font-size: 16px;
      margin-right: 6px;
      display: flex;
      div {
        height: 13px;
        width: 3px;
        background: #3296fb;
        border-radius: 3px;
        transform: rotate(16deg);
      }
    }

    .hot-apps-text {
      font-size: 14px;
      font-weight: 600;
      color: #000000db;
    }
  }

  .alarm-app-content {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
    justify-content: center;
    height: 149px;
    .alarm-app-icon {
      display: flex;
      align-items: center;
      justify-content: center;

      .alarm-app-icon-img {
        width: 76px;
        height: 76px;
        object-fit: contain;
      }
    }

    .alarm-app-stats {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .alarm-app-number {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        line-height: 1;
        margin-bottom: 4px;

        .arrow-up {
          color: #52c41a;
          font-size: 16px;
          margin-left: 2px;
        }

        .arrow-down {
          color: #ff4d4f;
          font-size: 16px;
          margin-left: 2px;
        }

        .arrow-stable {
          color: #faad14;
          font-size: 16px;
          margin-left: 2px;
        }
      }

      .alarm-app-label {
        font-size: 12px;
        color: #666;
        line-height: 1;
        white-space: nowrap;
      }
    }
  }

  // 资源概览容器样式
  .resource-overview-container {
    height: 96%;
    width: 100%;
    background: rgb(242, 242, 242);
    box-sizing: border-box;
    padding: 6px;
  }

  .resource-overview-grid {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 6px;
    padding: 10px;
  }

  .resource-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .resource-card {
    display: grid;
    place-items: center;
    background: #fff;
    border-radius: 5px;
    height: 60px;
  }

  .card-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .icon-container {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.icon-blue {
      background: #3a85ff49;
    }

    &.icon-green {
      background: #34c75954;
    }

    &.icon-cyan {
      background: #3abbf651;
    }
  }

  .card-icon {
    width: 24px;
    height: 24px;
    fill: white;
  }

  .card-icon-img {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  .card-text {
    .card-value {
      font-weight: bold;
      font-size: 16px;
    }

    .card-label {
      color: #666;
      font-size: 13px;
    }
  }

  .resource-load-container {
    display: flex;
    flex-direction: column;
    gap: 2px; // 减少组件之间的间距
    background: #fff;
    padding: 10px;
  }

  .system-run-container {
    min-height: 200px;
    padding: 5px;

    .alarm-overview-container,
    .alarm-type-container,
    .alarm-statistics-container {
      padding: 5px;
      height: 100%;
    }
  }

  .alarm-list-container {
    padding: 5px;
    height: 333px;
  }

  .user-data-container,
  .document-data-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .document-data-container {
    // padding: 10px 0;
    :deep(.data-grid) {
      flex-direction: row;
    }
  }

  .document-chart-container {
    flex: 1;
    // margin-top: 8px;
    background: #ffffff;
    // border: 1px solid #d9e4f5;
    // border-radius: 8px;
    padding: 12px;
    padding-top: 0;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .top5-title {
    font-size: 16px;
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 10px;
    padding-left: 5px;
    border-left: 3px solid #1976d2;
  }

  color: #2c3e50;
  margin: 0 !important;
  background: rgba(244, 245, 250, 0.6);
  min-height: 100vh;

  h3 {
    color: #000000d2;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  h4 {
    color: #000000d2;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  :deep(.border-box) {
    position: relative;
    background: #ffffff;
    border-top: none;

    margin-bottom: 8px;

    // 添加左边框渐变
    &::before {
      content: "";
      position: absolute;
      left: -1px;
      top: 0;
      width: 2px;
      height: 100%;
      border-radius: 0 0 0 8px;
    }

    // 添加右边框渐变
    &::after {
      content: "";
      position: absolute;
      right: -1px;
      top: 0;
      width: 2px;
      height: 100%;

      border-radius: 0 0 8px 0;
    }
  }

  .data-cards {
    display: flex;
    justify-content: space-between;

    > div {
      width: 48%;
    }
  }

  /* 响应式布局样式 */
  @media screen and (max-width: 1366px) {
    img[style*="height"] {
      transform: scale(0.9);
      transform-origin: left center;
    }

    .operation-bar {
      font-size: 12px;
      gap: 5px;
    }
  }

  @media screen and (max-width: 768px) {
    .system-run-container {
      flex-direction: column !important;

      .alarm-overview-container,
      .alarm-type-container,
      .alarm-statistics-container {
        width: 100% !important;
        margin-bottom: 10px;
      }
    }

    img[style*="height"] {
      transform: scale(0.8);
      transform-origin: left center;
    }
  }

  .operation-bar {
    display: flex;
    gap: 10px;
    padding-right: 10px;
    font-size: 14px;
    color: #2c3e50;
  }

  :deep(.document-data-container-user-usage-situation) {
    .stats-section {
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      margin-bottom: 0;
    }
  }

  // 时间范围指示器样式
  .time-range-indicator {
    position: absolute;
    top: 8px;
    right: 16px;
    z-index: 10;

    .indicator-text {
      font-size: 12px;
      color: #666;
      background: rgba(255, 255, 255, 0.9);
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;

      .loading-text {
        color: #409eff;
        font-weight: 500;
      }
    }
  }
}
</style>
