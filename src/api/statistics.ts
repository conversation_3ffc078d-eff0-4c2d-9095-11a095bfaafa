import { http } from '@/utils/http';
const baseUrl = 'app-server'
// 用户使用态势接口 - 参考 jd_app 的实现
export const userOverviewUserCount = () => http.get(baseUrl+'/userOverview/userCount');
export const userOverviewUsage = () => http.get(baseUrl+'/userOverview/usage');
export const userOverviewHotApp = (days: number) => http.get(baseUrl+`/userOverview/hotApp?day=${days}`);

// 公文应用态势接口 - 参考 jd_app 的实现
export const docAppTotalStats = (days: number) => http.get(baseUrl+`/docApp/totalStats?days=${days}`);
export const docAppTimeStats = (days: number) => http.get(baseUrl+`/docApp/timeStats?days=${days}`);
export const docAppDeptTop5 = (days: number) => http.get(baseUrl+`/docApp/deptTop5?days=${days}`);

// 资源监控相关接口
export const resourceOverview = (): Promise<ResourceOverviewData> =>
  http.get('/mdqs/resource/overview');

export const resourceCountByType = (): Promise<ResourceTypeData[]> =>
  http.get('/mdqs/resource/getCountGroupType');

export const resourceRoomList = (): Promise<ResourceRoomData[]> =>
  http.get('/mdqs/resource/getRoomName');

export const resourceCountByRoom = (params: ResourceCountByRoomParams): Promise<ResourceCountByRoomData> =>
  http.postJson('/mdqs/resource/getCountByRoomName', params);

// 资源使用率接口 - 获取CPU、内存、磁盘使用率
export const getPerUsageByRoomName = (params: PerUsageByRoomNameParams): Promise<PerUsageByRoomNameResponse> =>
  http.postJson('/mdqs/performance/getPerUsageByRoomName', params);

// 性能趋势图表接口 - 获取机房性能趋势数据
export const getPerTrendByRoomName = (params: PerTrendByRoomNameParams): Promise<PerTrendByRoomNameResponse> =>
  http.postJson('/mdqs/performance/getPerTrendByRoomName', params);

// 告警相关接口
export const getAlarmDetailPage = (params: AlarmDetailPageParams): Promise<AlarmDetailPageResponse> =>
  http.postJson('/mdqs/alarm/getAlarmDetailPage', params);

export const getAlarmCountGroupBySeverity = (params: AlarmCountParams): Promise<AlarmCountResponse> =>
  http.postJson('/mdqs/alarm/getAlarmCountGroupBySeverity', params);

// 告警概览接口 - 获取按严重级别分组的告警数量和时长
export const getAlarmCountAndDurBySeverity = (params: AlarmCountAndDurParams): Promise<AlarmCountAndDurResponse> =>
  http.postJson('/mdqs/alarm/getAlarmCountAndDurBySeverity', params);

// 告警类型分布接口 - 获取按类型分组的告警TOP5
export const getAlarmGroupByAndTop = (params: AlarmGroupByAndTopParams): Promise<AlarmGroupByAndTopResponse> =>
  http.postJson('/mdqs/alarm/groupByAndTop', params);

// 告警关联应用数量接口 - 获取告警关联的应用数量
export const alarmAppCount = (params: AlarmAppCountParams): Promise<AlarmAppCountResponse> =>
  http.postJson('/mdqs/alarm/alarmAppCount', params);

// 数据转换接口定义
export interface ApiResponse {
  userCount: number;
  usage: any;
  hotApps: any[];
}

// 资源监控接口数据类型定义
export interface ResourceOverviewData {
  roomCnt: number;    // 机房数量
  cabinetCnt: number; // 机柜数量
  devCnt: number;     // 设备数量
}

export interface ResourceTypeData {
  type: string;        // 设备类型（虚拟机、中间件、服务器、应用服务、云平台）
  count: number;       // 总数
  onlineCount: number; // 在线数
}

export interface ResourceRoomData {
  cabinetCnt: number;  // 机柜数量
  loadTime: string;    // 加载时间
  siteName: string;    // 站点名称
  zoneName: string;    // 区域名称
  roomID: number;      // 机房ID
  roomName: string;    // 机房名称
}

export interface ResourceCountByRoomParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {
    roomName: string;  // 机房名称
  };
}

export interface ResourceCountByRoomData {
  cabinetCnt: number;  // 机柜数量
  alarmCnt: number;    // 告警数量
  devCnt: number;      // 设备数量
}

// 资源使用率接口参数类型
export interface PerUsageByRoomNameParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {
    roomName: string;  // 机房名称
  };
}

// 资源使用率接口响应类型
export interface PerUsageByRoomNameResponse {
  status: string;
  data: {
    storageUsed: number | string;  // 磁盘使用率，可能是数字或"NaN"字符串
    cpuUsed: number | string;      // CPU使用率，可能是数字或"NaN"字符串
    memUsed: number | string;      // 内存使用率，可能是数字或"NaN"字符串
  };
  errors: any;
  msg: string;
  timestamp: number;
}

// 性能趋势图表接口参数类型
export interface PerTrendByRoomNameParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {
    roomName: string;  // 机房名称
    type: "min" | "hour";  // 数据类型：分钟级或小时级
  };
}

// 性能趋势图表数据项类型
export interface PerTrendDataItem {
  storageRate: number;  // 磁盘使用率
  memRate: number;      // 内存使用率
  cpuRate: number;      // CPU使用率
  time: string;         // x轴显示的时间，格式：HH:mm
  show: string;         // tooltip显示的时间，格式：YYYY-MM-DD HH:mm
}

// 性能趋势图表接口响应类型
export interface PerTrendByRoomNameResponse {
  status: string;
  data: PerTrendDataItem[];
  errors: any;
  msg: string;
  timestamp: number;
}

// 告警相关接口数据类型定义
export interface AlarmDetailPageParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {
    pageSize: number;  // 页面大小
    pageNum: number;   // 页码
  };
}

export interface AlarmDetailItem {
  lastOccurrence: string;  // 最近发生时间
  description: string;     // 告警描述
  origSeverity: number;    // 严重级别：2=提示，3=一般，4=重要，5=严重
  alertType: string;       // 告警类型
  devIp: string;          // 设备IP
  title: string;          // 告警标题
  status: string;         // 告警状态
}

export interface AlarmDetailPageResponse {
  status: string;
  data: {
    list: AlarmDetailItem[];  // 告警列表
    totalElements: number;    // 总数量
  };
}

export interface AlarmCountParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {};          // 空对象
}

export interface AlarmCountResponse {
  status: string;
  data: {
    "2"?: string;  // 提示告警数量
    "3"?: string;  // 一般告警数量
    "4"?: string;  // 重要告警数量
    "5"?: string;  // 严重告警数量
  };
}

// 告警概览接口参数类型
export interface AlarmCountAndDurParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {};          // 空对象
}

// 告警概览接口响应类型
export interface AlarmCountAndDurResponse {
  status: string;
  data: {
    "5_dur": number;    // 严重告警平均时长
    "2_cnt": number;    // 一般告警数量
    "total_cnt": number; // 总告警数量
    "3_cnt": number;    // 重要告警数量
    "4_cnt": number;    // 严重告警数量
    "5_cnt": number;    // 紧急告警数量
    "2_dur": number;    // 一般告警平均时长
    "total_dur": number; // 总告警平均时长
    "3_dur": number;    // 重要告警平均时长
    "4_dur": number;    // 严重告警平均时长
  };
  errors: any;
  msg: string;
  timestamp: number;
}

// 告警类型分布接口参数类型
export interface AlarmGroupByAndTopParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {
    top: number;       // 取前几条数据
    fields: string;    // 分组字段，如 "alertType"
  };
}

// 告警关联应用数量接口参数类型
export interface AlarmAppCountParams {
  startTime: string;   // 开始时间 格式：YYYY-MM-DD
  endTime: string;     // 结束时间 格式：YYYY-MM-DD
  params: {
    top: number;       // 取前几条数据，设置为0表示不限制
    fields: string;    // 分组字段，固定为 "ciTypeId"
  };
}

// 告警类型分布接口响应类型
export interface AlarmGroupByAndTopResponse {
  status: string;
  data: {
    count: number;     // 告警数量
    type: string;      // 告警类型
  }[];
  errors: any;
  msg: string;
  timestamp: number;
}

// 告警关联应用数量接口响应类型
export interface AlarmAppCountResponse {
  status: string;
  data: number;        // 告警关联应用数量
  errors: any;
  msg: string;
  timestamp: number;
}

// 数据转换方法
export const transformUserData = (data: ApiResponse) => ({
  rows: [
    {
      id: 'user-stats',
      items: [
        {
          id: 'total-users',
          value: data.userCount,
          label: '用户总数',
          unit: '人'
        }
      ]
    }
  ],
  hotApps: data.hotApps?.map((app, index) => ({
    id: app.id,
    name: app.name,
    count: app.count,
    rank: index + 1
  })) || []
});

// ==================== 系统架构相关接口 ====================

// 系统架构资源和告警统计参数
export interface ResAndAlarmCntParams {
  startTime: string;  // 开始时间，精确到秒
  endTime: string;    // 结束时间，精确到秒
  params: {};         // 空对象参数
}

// 设备详情接口
export interface DeviceDetail {
  alarmCount: number;  // 告警数量
  count: number;       // 设备数量
}

// 子类型设备详情（中间件层使用）
export interface SubTypeDevice {
  [deviceName: string]: DeviceDetail;  // 具体设备名称到详情的映射
}

// 设备类型详情
export interface DeviceTypeDetail {
  subType?: {
    [subTypeName: string]: {
      viSubType?: SubTypeDevice;  // 中间件层的具体设备
    } | DeviceDetail;  // 基础设施层和应用服务层的直接设备详情
  };
}

// 层级类型详情
export interface LayerTypeDetail {
  [layerName: string]: {
    subType: {
      [deviceTypeName: string]: DeviceTypeDetail | DeviceDetail;
    };
  };
}

// 机房资源和告警统计数据
export interface RoomResAndAlarmData {
  type: LayerTypeDetail;
}

// 系统架构资源和告警统计响应
export interface ResAndAlarmCntResponse {
  status: string;
  data: {
    [roomName: string]: RoomResAndAlarmData;
  };
  errors: null;
  msg: string;
  timestamp: number;
}

// 系统架构资源和告警统计接口
export const getResAndAlarmCntGroupByRoomName = (params: ResAndAlarmCntParams) => {
  return http.postJson<ResAndAlarmCntResponse>("/mdqs/resource/getResAndAlarmCntGroupByRoomName", params);
};