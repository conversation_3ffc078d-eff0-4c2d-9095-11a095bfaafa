import type { Plugin } from "vite";
import dayjs, { Dayjs } from "dayjs";
import utils from "@pureadmin/utils";
import duration from "dayjs/plugin/duration";
import { green, blue, red, bold } from "picocolors";
dayjs.extend(duration);

export function viteBuildInfo(): Plugin {
  let config: { command: string };
  let startTime: Dayjs;
  let endTime: Dayjs;
  let outDir: string;
  return {
    name: "vite:buildInfo",
    configResolved(resolvedConfig) {
      config = resolvedConfig;
      outDir = resolvedConfig.build?.outDir ?? "web-server-ui";
    },
    buildStart() {
      console.log(
        bold(
          green(
            `👏欢迎使用${blue("[durdoo-portal-framework]")}，开始编译程序 ...`
          )
        )
      );
      if (config.command === "build") {
        startTime = dayjs(new Date());
      }
    },
    closeBundle() {
      if (config.command === "build") {
        endTime = dayjs(new Date());
        utils.getPackageSize({
          folder: outDir,
          callback: (size: string) => {
            console.log(
              bold(
                green(
                  `🎉恭喜编译完成（总用时：${red(
                    dayjs.duration(endTime.diff(startTime)).format("mm分ss秒")
                  )}，编译后的大小为：${red(size)}）`
                )
              )
            );
          }
        });
      }
    }
  };
}
